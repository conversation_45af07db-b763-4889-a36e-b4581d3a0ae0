{"ast": null, "code": "var _jsxFileName = \"D:\\\\DevTools\\\\mllmjiuhangv1\\\\frontend\\\\src\\\\components\\\\ResizableSplitter.tsx\",\n  _s = $RefreshSig$();\nimport React, { useState, useRef, useEffect } from 'react';\nimport './ResizableSplitter.css';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst ResizableSplitter = ({\n  onResize,\n  initialWidth = 300,\n  minWidth = 200,\n  maxWidth = 600\n}) => {\n  _s();\n  const [isResizing, setIsResizing] = useState(false);\n  const splitterRef = useRef(null);\n  useEffect(() => {\n    const handleMouseMove = e => {\n      if (!isResizing) return;\n      const newWidth = Math.max(minWidth, Math.min(maxWidth, e.clientX));\n      onResize(newWidth);\n    };\n    const handleTouchMove = e => {\n      if (!isResizing || !e.touches[0]) return;\n      const newWidth = Math.max(minWidth, Math.min(maxWidth, e.touches[0].clientX));\n      onResize(newWidth);\n    };\n    const handleMouseUp = () => {\n      setIsResizing(false);\n    };\n    const handleTouchEnd = () => {\n      setIsResizing(false);\n    };\n    if (isResizing) {\n      document.addEventListener('mousemove', handleMouseMove);\n      document.addEventListener('mouseup', handleMouseUp);\n      document.addEventListener('touchmove', handleTouchMove);\n      document.addEventListener('touchend', handleTouchEnd);\n    }\n    return () => {\n      document.removeEventListener('mousemove', handleMouseMove);\n      document.removeEventListener('mouseup', handleMouseUp);\n      document.removeEventListener('touchmove', handleTouchMove);\n      document.removeEventListener('touchend', handleTouchEnd);\n    };\n  }, [isResizing, minWidth, maxWidth, onResize]);\n  const handleMouseDown = e => {\n    e.preventDefault();\n    setIsResizing(true);\n  };\n  const handleTouchStart = e => {\n    e.preventDefault();\n    setIsResizing(true);\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    ref: splitterRef,\n    className: \"resizable-splitter\",\n    onMouseDown: handleMouseDown,\n    onTouchStart: handleTouchStart\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 69,\n    columnNumber: 5\n  }, this);\n};\n_s(ResizableSplitter, \"C3WnAKj32gs741CsXljgzf5WKg8=\");\n_c = ResizableSplitter;\nexport default ResizableSplitter;\nvar _c;\n$RefreshReg$(_c, \"ResizableSplitter\");", "map": {"version": 3, "names": ["React", "useState", "useRef", "useEffect", "jsxDEV", "_jsxDEV", "ResizableSplitter", "onResize", "initialWidth", "min<PERSON><PERSON><PERSON>", "max<PERSON><PERSON><PERSON>", "_s", "isResizing", "setIsResizing", "splitterRef", "handleMouseMove", "e", "newWidth", "Math", "max", "min", "clientX", "handleTouchMove", "touches", "handleMouseUp", "handleTouchEnd", "document", "addEventListener", "removeEventListener", "handleMouseDown", "preventDefault", "handleTouchStart", "ref", "className", "onMouseDown", "onTouchStart", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "_c", "$RefreshReg$"], "sources": ["D:/DevTools/mllmjiuhangv1/frontend/src/components/ResizableSplitter.tsx"], "sourcesContent": ["import React, { useState, useRef, useEffect } from 'react';\nimport './ResizableSplitter.css';\n\ninterface ResizableSplitterProps {\n  onResize: (newWidth: number) => void;\n  initialWidth?: number;\n  minWidth?: number;\n  maxWidth?: number;\n}\n\nconst ResizableSplitter: React.FC<ResizableSplitterProps> = ({ \n  onResize, \n  initialWidth = 300, \n  minWidth = 200, \n  maxWidth = 600 \n}) => {\n  const [isResizing, setIsResizing] = useState(false);\n  const splitterRef = useRef<HTMLDivElement>(null);\n\n  useEffect(() => {\n    const handleMouseMove = (e: MouseEvent) => {\n      if (!isResizing) return;\n      \n      const newWidth = Math.max(minWidth, Math.min(maxWidth, e.clientX));\n      onResize(newWidth);\n    };\n\n    const handleTouchMove = (e: TouchEvent) => {\n      if (!isResizing || !e.touches[0]) return;\n      \n      const newWidth = Math.max(minWidth, Math.min(maxWidth, e.touches[0].clientX));\n      onResize(newWidth);\n    };\n\n    const handleMouseUp = () => {\n      setIsResizing(false);\n    };\n\n    const handleTouchEnd = () => {\n      setIsResizing(false);\n    };\n\n    if (isResizing) {\n      document.addEventListener('mousemove', handleMouseMove);\n      document.addEventListener('mouseup', handleMouseUp);\n      document.addEventListener('touchmove', handleTouchMove);\n      document.addEventListener('touchend', handleTouchEnd);\n    }\n\n    return () => {\n      document.removeEventListener('mousemove', handleMouseMove);\n      document.removeEventListener('mouseup', handleMouseUp);\n      document.removeEventListener('touchmove', handleTouchMove);\n      document.removeEventListener('touchend', handleTouchEnd);\n    };\n  }, [isResizing, minWidth, maxWidth, onResize]);\n\n  const handleMouseDown = (e: React.MouseEvent) => {\n    e.preventDefault();\n    setIsResizing(true);\n  };\n\n  const handleTouchStart = (e: React.TouchEvent) => {\n    e.preventDefault();\n    setIsResizing(true);\n  };\n\n  return (\n    <div \n      ref={splitterRef}\n      className=\"resizable-splitter\"\n      onMouseDown={handleMouseDown}\n      onTouchStart={handleTouchStart}\n    />\n  );\n};\n\nexport default ResizableSplitter;"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,MAAM,EAAEC,SAAS,QAAQ,OAAO;AAC1D,OAAO,yBAAyB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AASjC,MAAMC,iBAAmD,GAAGA,CAAC;EAC3DC,QAAQ;EACRC,YAAY,GAAG,GAAG;EAClBC,QAAQ,GAAG,GAAG;EACdC,QAAQ,GAAG;AACb,CAAC,KAAK;EAAAC,EAAA;EACJ,MAAM,CAACC,UAAU,EAAEC,aAAa,CAAC,GAAGZ,QAAQ,CAAC,KAAK,CAAC;EACnD,MAAMa,WAAW,GAAGZ,MAAM,CAAiB,IAAI,CAAC;EAEhDC,SAAS,CAAC,MAAM;IACd,MAAMY,eAAe,GAAIC,CAAa,IAAK;MACzC,IAAI,CAACJ,UAAU,EAAE;MAEjB,MAAMK,QAAQ,GAAGC,IAAI,CAACC,GAAG,CAACV,QAAQ,EAAES,IAAI,CAACE,GAAG,CAACV,QAAQ,EAAEM,CAAC,CAACK,OAAO,CAAC,CAAC;MAClEd,QAAQ,CAACU,QAAQ,CAAC;IACpB,CAAC;IAED,MAAMK,eAAe,GAAIN,CAAa,IAAK;MACzC,IAAI,CAACJ,UAAU,IAAI,CAACI,CAAC,CAACO,OAAO,CAAC,CAAC,CAAC,EAAE;MAElC,MAAMN,QAAQ,GAAGC,IAAI,CAACC,GAAG,CAACV,QAAQ,EAAES,IAAI,CAACE,GAAG,CAACV,QAAQ,EAAEM,CAAC,CAACO,OAAO,CAAC,CAAC,CAAC,CAACF,OAAO,CAAC,CAAC;MAC7Ed,QAAQ,CAACU,QAAQ,CAAC;IACpB,CAAC;IAED,MAAMO,aAAa,GAAGA,CAAA,KAAM;MAC1BX,aAAa,CAAC,KAAK,CAAC;IACtB,CAAC;IAED,MAAMY,cAAc,GAAGA,CAAA,KAAM;MAC3BZ,aAAa,CAAC,KAAK,CAAC;IACtB,CAAC;IAED,IAAID,UAAU,EAAE;MACdc,QAAQ,CAACC,gBAAgB,CAAC,WAAW,EAAEZ,eAAe,CAAC;MACvDW,QAAQ,CAACC,gBAAgB,CAAC,SAAS,EAAEH,aAAa,CAAC;MACnDE,QAAQ,CAACC,gBAAgB,CAAC,WAAW,EAAEL,eAAe,CAAC;MACvDI,QAAQ,CAACC,gBAAgB,CAAC,UAAU,EAAEF,cAAc,CAAC;IACvD;IAEA,OAAO,MAAM;MACXC,QAAQ,CAACE,mBAAmB,CAAC,WAAW,EAAEb,eAAe,CAAC;MAC1DW,QAAQ,CAACE,mBAAmB,CAAC,SAAS,EAAEJ,aAAa,CAAC;MACtDE,QAAQ,CAACE,mBAAmB,CAAC,WAAW,EAAEN,eAAe,CAAC;MAC1DI,QAAQ,CAACE,mBAAmB,CAAC,UAAU,EAAEH,cAAc,CAAC;IAC1D,CAAC;EACH,CAAC,EAAE,CAACb,UAAU,EAAEH,QAAQ,EAAEC,QAAQ,EAAEH,QAAQ,CAAC,CAAC;EAE9C,MAAMsB,eAAe,GAAIb,CAAmB,IAAK;IAC/CA,CAAC,CAACc,cAAc,CAAC,CAAC;IAClBjB,aAAa,CAAC,IAAI,CAAC;EACrB,CAAC;EAED,MAAMkB,gBAAgB,GAAIf,CAAmB,IAAK;IAChDA,CAAC,CAACc,cAAc,CAAC,CAAC;IAClBjB,aAAa,CAAC,IAAI,CAAC;EACrB,CAAC;EAED,oBACER,OAAA;IACE2B,GAAG,EAAElB,WAAY;IACjBmB,SAAS,EAAC,oBAAoB;IAC9BC,WAAW,EAAEL,eAAgB;IAC7BM,YAAY,EAAEJ;EAAiB;IAAAK,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAChC,CAAC;AAEN,CAAC;AAAC5B,EAAA,CAjEIL,iBAAmD;AAAAkC,EAAA,GAAnDlC,iBAAmD;AAmEzD,eAAeA,iBAAiB;AAAC,IAAAkC,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
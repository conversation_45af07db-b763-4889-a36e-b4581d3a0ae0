[{"D:\\DevTools\\mllmjiuhangv1\\frontend\\src\\index.tsx": "1", "D:\\DevTools\\mllmjiuhangv1\\frontend\\src\\reportWebVitals.ts": "2", "D:\\DevTools\\mllmjiuhangv1\\frontend\\src\\App.tsx": "3", "D:\\DevTools\\mllmjiuhangv1\\frontend\\src\\components\\ResizableSplitter.tsx": "4"}, {"size": 624, "mtime": 1753345203479, "results": "5", "hashOfConfig": "6"}, {"size": 425, "mtime": 1753338444997, "results": "7", "hashOfConfig": "6"}, {"size": 12551, "mtime": 1753362785776, "results": "8", "hashOfConfig": "6"}, {"size": 2104, "mtime": 1753360204815, "results": "9", "hashOfConfig": "6"}, {"filePath": "10", "messages": "11", "suppressedMessages": "12", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "1w45fwz", {"filePath": "13", "messages": "14", "suppressedMessages": "15", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "16", "messages": "17", "suppressedMessages": "18", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "19", "messages": "20", "suppressedMessages": "21", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "D:\\DevTools\\mllmjiuhangv1\\frontend\\src\\index.tsx", [], [], "D:\\DevTools\\mllmjiuhangv1\\frontend\\src\\reportWebVitals.ts", [], [], "D:\\DevTools\\mllmjiuhangv1\\frontend\\src\\App.tsx", [], [], "D:\\DevTools\\mllmjiuhangv1\\frontend\\src\\components\\ResizableSplitter.tsx", [], []]
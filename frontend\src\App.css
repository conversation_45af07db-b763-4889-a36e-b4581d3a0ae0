.App {
  min-height: 100vh;
  display: flex;
  flex-direction: column;
  font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
}

.App-header {
  background-color: #282c34;
  color: white;
  padding: 1rem;
  text-align: center;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

/* 响应式设计：根据屏幕宽度调整header */
@media (max-width: 768px) {
  .App-header {
    padding: 0.5rem;
  }
}

.App-main {
  flex: 1;
  padding: 2rem;
  max-width: 100%;
  margin: 0 auto;
  width: 100%;
  display: flex;
  flex-direction: row;
}

/* 响应式设计：根据屏幕宽度调整主内容区域 */
@media (max-width: 1200px) {
  .App-main {
    padding: 1.5rem;
  }
}

@media (max-width: 768px) {
  .App-main {
    padding: 1rem;
    flex-direction: column; /* 在小屏幕上改为列布局 */
  }

  .detection-controls {
    flex-direction: column;
  }

  .detect-btn {
    align-self: stretch;
  }
}

.App-footer {
  background-color: #282c34;
  color: white;
  text-align: center;
  padding: 1rem;
}

/* 响应式设计：根据屏幕宽度调整footer */
@media (max-width: 768px) {
  .App-footer {
    padding: 0.5rem;
  }
}

/* 左侧控制面板 */
.control-panel {
  display: flex;
  flex-direction: column;
  gap: 2rem;
  overflow: hidden;
  width: 100%;
  align-items: flex-start; /* 左侧靠拢对齐 */
}

/* 右侧内容区域 */
.content-area {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 2rem;
}

/* 响应式分隔器：在小屏幕上隐藏 */
.responsive-divider {
  display: block;
  width: 3px;
}

@media (max-width: 768px) {
  .responsive-divider {
    display: none;
  }
}

.upload-container {
  margin-bottom: 0; /* 移除原有的margin */
}

.drop-area {
  border: 2px dashed #ccc;
  border-radius: 8px;
  padding: 2rem;
  text-align: center;
  cursor: pointer;
  transition: all 0.3s ease;
  position: relative;
}

.drop-area:hover,
.drop-area:focus-within {
  border-color: #61dafb;
  background-color: rgba(97, 218, 251, 0.1);
}

.drop-area-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}

.preview-container {
  position: relative;
  overflow: hidden;
  max-height: 500px;
  width: 100%;
  margin-top: 1rem;
  border: 1px solid #ddd;
  border-radius: 4px;
  background-color: #f9f9f9;
}

.preview-controls {
  position: absolute;
  top: 10px;
  right: 10px;
  display: flex;
  gap: 10px;
  background-color: rgba(0, 0, 0, 0.5);
  padding: 5px 10px;
  border-radius: 4px;
  z-index: 10;
}

.control-btn {
  background-color: #61dafb;
  border: none;
  border-radius: 4px;
  padding: 5px 10px;
  cursor: pointer;
  font-size: 14px;
  transition: background-color 0.3s ease;
}

.control-btn:hover {
  background-color: #4cc0e5;
}

.detection-controls {
  display: flex;
  flex-direction: column; /* 修改为列布局 */
  gap: 1rem;
  margin-bottom: 0;
  order: 0; /* 重置order属性 */
  justify-content: flex-start;
  width: 100%; /* 确保宽度占满容器 */
}

.prompt-input {
  flex: 1;
  min-width: 200px;
}

.prompt-input label {
  display: block;
  margin-bottom: 0.5rem;
  font-weight: 500;
}

.prompt-input input,
.prompt-input select {
  width: 100%;
  padding: 0.75rem;
  border: 1px solid #ddd;
  border-radius: 4px;
  font-size: 16px;
}

.detect-btn {
  background-color: #61dafb;
  color: #282c34;
  border: none;
  border-radius: 4px;
  padding: 0.75rem 1.5rem;
  font-size: 16px;
  cursor: pointer;
  font-weight: 500;
  align-self: flex-start; /* 修改对齐方式 */
  transition: background-color 0.3s ease;
  width: 100%; /* 按钮宽度100% */
}

.detect-btn:hover:not(:disabled) {
  background-color: #4cc0e5;
}

.detect-btn:disabled {
  background-color: #ccc;
  cursor: not-allowed;
}

.error-message {
  background-color: #f8d7da;
  color: #721c24;
  padding: 1rem;
  border-radius: 4px;
  margin-bottom: 0; /* 移除原有的margin */
  border: 1px solid #f5c6cb;
}

.results-container,
.analysis-container {
  margin-bottom: 0; /* 移除原有的margin */
  background-color: white;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  padding: 1.5rem;
}

.results-container h2,
.analysis-container h2 {
  margin-top: 0;
  color: #282c34;
  border-bottom: 2px solid #61dafb;
  padding-bottom: 0.5rem;
}

.detection-results {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: 1rem;
  margin-top: 1rem;
}

.result-item {
  background-color: #f9f9f9;
  border-radius: 4px;
  padding: 1rem;
  border-left: 4px solid #61dafb;
  transition: transform 0.2s ease;
}

.result-item:hover {
  transform: translateY(-3px);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.result-class {
  font-weight: bold;
  display: block;
  margin-bottom: 0.25rem;
}

.result-score {
  color: #666;
  font-size: 0.9rem;
  display: block;
  margin-bottom: 0.5rem;
}

.result-box {
  font-size: 0.9rem;
  color: #333;
}

.no-results,
.no-analysis {
  color: #666;
  font-style: italic;
  padding: 1rem 0;
}

.analysis-summary {
  font-size: 1.1rem;
  margin-bottom: 1rem;
  line-height: 1.6;
}

.class-distribution,
.analysis-details {
  margin-bottom: 1rem;
}

.class-distribution h3,
.analysis-details h3 {
  font-size: 1rem;
  margin-bottom: 0.5rem;
  color: #282c34;
}

.class-distribution ul,
.analysis-details ul {
  padding-left: 1.5rem;
  margin: 0;
}

.class-distribution li,
.analysis-details li {
  margin-bottom: 0.25rem;
}

@media (max-width: 768px) {
  .App-main {
    padding: 1rem;
    flex-direction: column; /* 在小屏幕上改为列布局 */
  }

  .control-panel {
    flex: 0 0 auto; /* 在小屏幕上自适应 */
  }

  .detection-controls {
    flex-direction: column;
  }

  .detect-btn {
    align-self: stretch;
  }
}

/* 动画效果 */
@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.detection-results,
.analysis-results {
  animation: fadeIn 0.5s ease-out;
}

/* 加载动画 */
@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.detect-btn:disabled::after {
  content: '';
  display: inline-block;
  width: 16px;
  height: 16px;
  margin-left: 8px;
  border: 2px solid rgba(0, 0, 0, 0.3);
  border-radius: 50%;
  border-top-color: #282c34;
  animation: spin 1s ease-in-out infinite;
}

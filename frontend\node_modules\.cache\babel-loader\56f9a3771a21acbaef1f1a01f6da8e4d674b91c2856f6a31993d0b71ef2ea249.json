{"ast": null, "code": "var _jsxFileName = \"D:\\\\DevTools\\\\mllmjiuhangv1\\\\frontend\\\\src\\\\App.tsx\",\n  _s = $RefreshSig$();\nimport React, { useState, useRef, useEffect } from 'react';\nimport { useDropzone } from 'react-dropzone';\nimport axios from 'axios';\nimport { TransformComponent, TransformWrapper } from 'react-zoom-pan-pinch';\nimport './App.css';\n\n// 定义提示词模板类型\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\n// 预定义的提示词模板\nconst PROMPT_TEMPLATES = [{\n  id: 'default',\n  name: '默认检测',\n  prompt: '检测图像中的所有目标'\n}, {\n  id: 'person',\n  name: '人员检测',\n  prompt: '检测图像中的人员'\n}, {\n  id: 'vehicle',\n  name: '车辆检测',\n  prompt: '检测图像中的车辆，包括汽车、卡车、摩托车等'\n}, {\n  id: 'helicopter',\n  name: '直升机检测',\n  prompt: '检测图像中的直升机'\n}, {\n  id: 'airplane',\n  name: '飞机检测',\n  prompt: '检测图像中的飞机'\n}, {\n  id: 'ship',\n  name: '船舶检测',\n  prompt: '检测图像中的船舶'\n}, {\n  id: 'animal',\n  name: '动物检测',\n  prompt: '检测图像中的动物'\n}, {\n  id: 'building',\n  name: '建筑物检测',\n  prompt: '检测图像中的建筑物'\n}];\nfunction App() {\n  _s();\n  const [selectedImage, setSelectedImage] = useState(null);\n  const [previewUrl, setPreviewUrl] = useState(null);\n  const [detectionPrompt, setDetectionPrompt] = useState('');\n  const [selectedTemplate, setSelectedTemplate] = useState('default');\n  const [detectionResults, setDetectionResults] = useState([]);\n  const [situationAnalysis, setSituationAnalysis] = useState(null);\n  const [isLoading, setIsLoading] = useState(false);\n  const [error, setError] = useState(null);\n  const canvasRef = useRef(null);\n\n  // 处理拖放文件\n  const {\n    getRootProps,\n    getInputProps\n  } = useDropzone({\n    multiple: false,\n    // 只允许上传单个文件\n    accept: {\n      'image/*': ['.jpeg', '.jpg', '.png', '.webp']\n    },\n    onDrop: acceptedFiles => {\n      if (acceptedFiles.length > 0) {\n        processFile(acceptedFiles[0]);\n      }\n    },\n    onDragEnter: () => {},\n    // 空函数实现 onDragEnter\n    onDragOver: () => {},\n    // 空函数实现 onDragOver\n    onDragLeave: () => {} // 空函数实现 onDragLeave\n  });\n\n  // 处理文件\n  const processFile = file => {\n    setSelectedImage(file);\n    const url = URL.createObjectURL(file);\n    setPreviewUrl(url);\n    setDetectionResults([]);\n    setSituationAnalysis(null);\n    setError(null);\n  };\n\n  // 处理检测请求\n  const handleDetect = async () => {\n    if (!selectedImage) {\n      setError('请先上传图像');\n      return;\n    }\n    setIsLoading(true);\n    setError(null);\n    try {\n      const formData = new FormData();\n      formData.append('image', selectedImage);\n      formData.append('prompt', detectionPrompt);\n      const response = await axios.post('http://localhost:5000/api/detect', formData, {\n        headers: {\n          'Content-Type': 'multipart/form-data'\n        }\n      });\n      setDetectionResults(response.data.detections || []);\n      setSituationAnalysis(response.data.situation_analysis || null);\n    } catch (err) {\n      setError(err instanceof Error ? err.message : '检测过程中发生错误');\n    } finally {\n      setIsLoading(false);\n    }\n  };\n\n  // 当选择模板时更新提示词\n  const handleTemplateChange = templateId => {\n    setSelectedTemplate(templateId);\n    const template = PROMPT_TEMPLATES.find(t => t.id === templateId);\n    if (template) {\n      setDetectionPrompt(template.prompt);\n    }\n  };\n\n  // 绘制图像和检测结果\n  useEffect(() => {\n    if (!canvasRef.current || !previewUrl) return;\n    const canvas = canvasRef.current;\n    const ctx = canvas.getContext('2d');\n    if (!ctx) return;\n    const img = new Image();\n    img.onload = () => {\n      // 设置画布大小\n      canvas.width = img.width;\n      canvas.height = img.height;\n\n      // 清除画布\n      ctx.clearRect(0, 0, canvas.width, canvas.height);\n\n      // 绘制图像\n      ctx.drawImage(img, 0, 0);\n\n      // 绘制检测结果\n      detectionResults.forEach(result => {\n        const {\n          box,\n          class: className,\n          score\n        } = result;\n        const {\n          xmin,\n          ymin,\n          xmax,\n          ymax\n        } = box;\n\n        // 绘制边界框\n        ctx.strokeStyle = 'rgba(255, 0, 0, 0.8)';\n        ctx.lineWidth = 2;\n        ctx.strokeRect(xmin, ymin, xmax - xmin, ymax - ymin);\n\n        // 绘制标签\n        ctx.fillStyle = 'rgba(255, 0, 0, 0.8)';\n        ctx.fillRect(xmin, ymin - 20, xmax - xmin, 20);\n        ctx.fillStyle = 'white';\n        ctx.font = '12px Arial';\n        ctx.fillText(`${className} (${score.toFixed(2)})`, xmin + 5, ymin - 5);\n      });\n    };\n    img.src = previewUrl;\n  }, [previewUrl, detectionResults]);\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"App\",\n    children: [/*#__PURE__*/_jsxDEV(\"header\", {\n      className: \"App-header\",\n      children: /*#__PURE__*/_jsxDEV(\"h1\", {\n        children: \"\\u591A\\u6A21\\u6001\\u56FE\\u50CF\\u8BED\\u4E49\\u7406\\u89E3\\u7CFB\\u7EDF\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 142,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 141,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"main\", {\n      className: \"App-main\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"control-panel\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"detection-controls\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"prompt-input\",\n            children: [/*#__PURE__*/_jsxDEV(\"label\", {\n              htmlFor: \"detection-prompt-template\",\n              children: \"\\u76EE\\u6807\\u68C0\\u6D4B\\u63D0\\u793A\\u8BCD\\u6A21\\u677F:\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 149,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"select\", {\n              id: \"detection-prompt-template\",\n              value: selectedTemplate,\n              onChange: e => handleTemplateChange(e.target.value),\n              className: \"template-select\",\n              children: PROMPT_TEMPLATES.map(template => /*#__PURE__*/_jsxDEV(\"option\", {\n                value: template.id,\n                children: template.name\n              }, template.id, false, {\n                fileName: _jsxFileName,\n                lineNumber: 157,\n                columnNumber: 19\n              }, this))\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 150,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 148,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"prompt-input\",\n            children: [/*#__PURE__*/_jsxDEV(\"label\", {\n              htmlFor: \"detection-prompt\",\n              children: \"\\u81EA\\u5B9A\\u4E49\\u76EE\\u6807\\u68C0\\u6D4B\\u63D0\\u793A\\u8BCD:\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 164,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n              type: \"text\",\n              id: \"detection-prompt\",\n              value: detectionPrompt,\n              onChange: e => setDetectionPrompt(e.target.value),\n              placeholder: \"\\u8F93\\u5165\\u8981\\u68C0\\u6D4B\\u7684\\u76EE\\u6807\\u7C7B\\u522B\\uFF0C\\u5982\\u4EBA\\u3001\\u8F66\\u8F86\\u3001\\u76F4\\u5347\\u673A...\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 165,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 163,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            className: \"detect-btn\",\n            onClick: handleDetect,\n            disabled: !previewUrl || isLoading,\n            children: isLoading ? '检测中...' : '开始检测目标'\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 173,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 147,\n          columnNumber: 11\n        }, this), error && /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"error-message\",\n          children: error\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 182,\n          columnNumber: 21\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 146,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"content-area\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"upload-container\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            ...getRootProps(),\n            className: \"drop-area\",\n            children: [/*#__PURE__*/_jsxDEV(\"input\", {\n              ...getInputProps()\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 189,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"drop-area-content\",\n              children: !previewUrl ? /*#__PURE__*/_jsxDEV(_Fragment, {\n                children: [/*#__PURE__*/_jsxDEV(\"svg\", {\n                  xmlns: \"http://www.w3.org/2000/svg\",\n                  width: \"48\",\n                  height: \"48\",\n                  viewBox: \"0 0 24 24\",\n                  fill: \"none\",\n                  stroke: \"currentColor\",\n                  strokeWidth: \"2\",\n                  strokeLinecap: \"round\",\n                  strokeLinejoin: \"round\",\n                  children: [/*#__PURE__*/_jsxDEV(\"path\", {\n                    d: \"M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 204,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"polyline\", {\n                    points: \"7 10 12 15 17 10\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 205,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"line\", {\n                    x1: \"12\",\n                    y1: \"15\",\n                    x2: \"12\",\n                    y2: \"3\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 206,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 193,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                  children: \"\\u62D6\\u653E\\u56FE\\u50CF\\u5230\\u6B64\\u5904\\u6216\\u70B9\\u51FB\\u9009\\u62E9\\u6587\\u4EF6\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 208,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true) : /*#__PURE__*/_jsxDEV(TransformWrapper, {\n                initialScale: 1,\n                initialPositionX: 0,\n                initialPositionY: 0,\n                children: ({\n                  zoomIn,\n                  zoomOut,\n                  resetTransform\n                }) => /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"preview-container\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"preview-controls\",\n                    children: [/*#__PURE__*/_jsxDEV(\"button\", {\n                      onClick: () => zoomIn(),\n                      className: \"control-btn\",\n                      children: \"\\u653E\\u5927\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 223,\n                      columnNumber: 27\n                    }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                      onClick: () => zoomOut(),\n                      className: \"control-btn\",\n                      children: \"\\u7F29\\u5C0F\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 224,\n                      columnNumber: 27\n                    }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                      onClick: resetTransform,\n                      className: \"control-btn\",\n                      children: \"\\u91CD\\u7F6E\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 225,\n                      columnNumber: 27\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 222,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(TransformComponent, {\n                    children: /*#__PURE__*/_jsxDEV(\"canvas\", {\n                      ref: canvasRef,\n                      className: \"preview-canvas\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 228,\n                      columnNumber: 27\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 227,\n                    columnNumber: 25\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 221,\n                  columnNumber: 23\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 211,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 190,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 188,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 187,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"results-container\",\n          children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n            children: \"\\u68C0\\u6D4B\\u7ED3\\u679C\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 239,\n            columnNumber: 13\n          }, this), detectionResults.length > 0 ? /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"detection-results\",\n            children: detectionResults.map((result, index) => /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"result-item\",\n              children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"result-class\",\n                children: result.class\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 244,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"result-score\",\n                children: [\"\\u7F6E\\u4FE1\\u5EA6: \", result.score.toFixed(3)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 245,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"result-box\",\n                children: [\"\\u4F4D\\u7F6E: (\", result.box.xmin.toFixed(1), \", \", result.box.ymin.toFixed(1), \") \\u5230 (\", result.box.xmax.toFixed(1), \", \", result.box.ymax.toFixed(1), \")\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 246,\n                columnNumber: 21\n              }, this)]\n            }, index, true, {\n              fileName: _jsxFileName,\n              lineNumber: 243,\n              columnNumber: 19\n            }, this))\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 241,\n            columnNumber: 15\n          }, this) : /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"no-results\",\n            children: \"\\u6682\\u65E0\\u68C0\\u6D4B\\u7ED3\\u679C\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 253,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 238,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"analysis-container\",\n          children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n            children: \"\\u6001\\u52BF\\u5206\\u6790\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 258,\n            columnNumber: 13\n          }, this), situationAnalysis ? /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"analysis-results\",\n            children: [/*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"analysis-summary\",\n              children: situationAnalysis.summary\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 261,\n              columnNumber: 17\n            }, this), situationAnalysis.class_distribution && /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"class-distribution\",\n              children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n                children: \"\\u7C7B\\u522B\\u5206\\u5E03\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 264,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"ul\", {\n                children: Object.entries(situationAnalysis.class_distribution).map(([cls, count]) => /*#__PURE__*/_jsxDEV(\"li\", {\n                  children: [cls, \": \", count, \"\\u4E2A\"]\n                }, cls, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 267,\n                  columnNumber: 25\n                }, this))\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 265,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 263,\n              columnNumber: 19\n            }, this), situationAnalysis.details && /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"analysis-details\",\n              children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n                children: \"\\u8BE6\\u7EC6\\u4FE1\\u606F\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 274,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"ul\", {\n                children: situationAnalysis.details.map((detail, index) => /*#__PURE__*/_jsxDEV(\"li\", {\n                  children: detail\n                }, index, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 277,\n                  columnNumber: 25\n                }, this))\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 275,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 273,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 260,\n            columnNumber: 15\n          }, this) : /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"no-analysis\",\n            children: \"\\u6682\\u65E0\\u6001\\u52BF\\u5206\\u6790\\u7ED3\\u679C\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 284,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 257,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 186,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 144,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"footer\", {\n      className: \"App-footer\",\n      children: /*#__PURE__*/_jsxDEV(\"p\", {\n        children: \"\\xA9 2025 \\u591A\\u6A21\\u6001\\u56FE\\u50CF\\u8BED\\u4E49\\u7406\\u89E3\\u7CFB\\u7EDF\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 290,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 289,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 140,\n    columnNumber: 5\n  }, this);\n}\n_s(App, \"iOeZ9sEhk3+wgfYBI+kmnMbQK5Q=\", false, function () {\n  return [useDropzone];\n});\n_c = App;\nexport default App;\nvar _c;\n$RefreshReg$(_c, \"App\");", "map": {"version": 3, "names": ["React", "useState", "useRef", "useEffect", "useDropzone", "axios", "TransformComponent", "TransformWrapper", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "PROMPT_TEMPLATES", "id", "name", "prompt", "App", "_s", "selectedImage", "setSelectedImage", "previewUrl", "setPreviewUrl", "detectionPrompt", "setDetectionPrompt", "selectedTemplate", "setSelectedTemplate", "detectionResults", "setDetectionResults", "situationAnalysis", "setSituationAnalysis", "isLoading", "setIsLoading", "error", "setError", "canvasRef", "getRootProps", "getInputProps", "multiple", "accept", "onDrop", "acceptedFiles", "length", "processFile", "onDragEnter", "onDragOver", "onDragLeave", "file", "url", "URL", "createObjectURL", "handleDetect", "formData", "FormData", "append", "response", "post", "headers", "data", "detections", "situation_analysis", "err", "Error", "message", "handleTemplateChange", "templateId", "template", "find", "t", "current", "canvas", "ctx", "getContext", "img", "Image", "onload", "width", "height", "clearRect", "drawImage", "for<PERSON>ach", "result", "box", "class", "className", "score", "xmin", "ymin", "xmax", "ymax", "strokeStyle", "lineWidth", "strokeRect", "fillStyle", "fillRect", "font", "fillText", "toFixed", "src", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "htmlFor", "value", "onChange", "e", "target", "map", "type", "placeholder", "onClick", "disabled", "xmlns", "viewBox", "fill", "stroke", "strokeWidth", "strokeLinecap", "strokeLinejoin", "d", "points", "x1", "y1", "x2", "y2", "initialScale", "initialPositionX", "initialPositionY", "zoomIn", "zoomOut", "resetTransform", "ref", "index", "summary", "class_distribution", "Object", "entries", "cls", "count", "details", "detail", "_c", "$RefreshReg$"], "sources": ["D:/DevTools/mllmjiuhangv1/frontend/src/App.tsx"], "sourcesContent": ["import React, { useState, useRef, useEffect } from 'react';\nimport { useDropzone } from 'react-dropzone';\nimport axios from 'axios';\nimport { TransformComponent, TransformWrapper } from 'react-zoom-pan-pinch';\nimport './App.css';\n\n// 定义提示词模板类型\ninterface PromptTemplate {\n  id: string;\n  name: string;\n  prompt: string;\n}\n\n// 预定义的提示词模板\nconst PROMPT_TEMPLATES: PromptTemplate[] = [\n  { id: 'default', name: '默认检测', prompt: '检测图像中的所有目标' },\n  { id: 'person', name: '人员检测', prompt: '检测图像中的人员' },\n  { id: 'vehicle', name: '车辆检测', prompt: '检测图像中的车辆，包括汽车、卡车、摩托车等' },\n  { id: 'helicopter', name: '直升机检测', prompt: '检测图像中的直升机' },\n  { id: 'airplane', name: '飞机检测', prompt: '检测图像中的飞机' },\n  { id: 'ship', name: '船舶检测', prompt: '检测图像中的船舶' },\n  { id: 'animal', name: '动物检测', prompt: '检测图像中的动物' },\n  { id: 'building', name: '建筑物检测', prompt: '检测图像中的建筑物' },\n];\n\nfunction App() {\n  const [selectedImage, setSelectedImage] = useState<File | null>(null);\n  const [previewUrl, setPreviewUrl] = useState<string | null>(null);\n  const [detectionPrompt, setDetectionPrompt] = useState<string>('');\n  const [selectedTemplate, setSelectedTemplate] = useState<string>('default');\n  const [detectionResults, setDetectionResults] = useState<any[]>([]);\n  const [situationAnalysis, setSituationAnalysis] = useState<any>(null);\n  const [isLoading, setIsLoading] = useState<boolean>(false);\n  const [error, setError] = useState<string | null>(null);\n  const canvasRef = useRef<HTMLCanvasElement>(null);\n\n  // 处理拖放文件\n  const { getRootProps, getInputProps } = useDropzone({\n    multiple: false, // 只允许上传单个文件\n    accept: { 'image/*': ['.jpeg', '.jpg', '.png', '.webp'] },\n    onDrop: (acceptedFiles) => {\n      if (acceptedFiles.length > 0) {\n        processFile(acceptedFiles[0]);\n      }\n    },\n    onDragEnter: () => {}, // 空函数实现 onDragEnter\n    onDragOver: () => {}, // 空函数实现 onDragOver\n    onDragLeave: () => {}, // 空函数实现 onDragLeave\n  });\n\n  // 处理文件\n  const processFile = (file: File) => {\n    setSelectedImage(file);\n    const url = URL.createObjectURL(file);\n    setPreviewUrl(url);\n    setDetectionResults([]);\n    setSituationAnalysis(null);\n    setError(null);\n  };\n\n  // 处理检测请求\n  const handleDetect = async () => {\n    if (!selectedImage) {\n      setError('请先上传图像');\n      return;\n    }\n\n    setIsLoading(true);\n    setError(null);\n\n    try {\n      const formData = new FormData();\n      formData.append('image', selectedImage);\n      formData.append('prompt', detectionPrompt);\n\n      const response = await axios.post('http://localhost:5000/api/detect', formData, {\n        headers: { 'Content-Type': 'multipart/form-data' }\n      });\n\n      setDetectionResults(response.data.detections || []);\n      setSituationAnalysis(response.data.situation_analysis || null);\n    } catch (err) {\n      setError(err instanceof Error ? err.message : '检测过程中发生错误');\n    } finally {\n      setIsLoading(false);\n    }\n  };\n\n  // 当选择模板时更新提示词\n  const handleTemplateChange = (templateId: string) => {\n    setSelectedTemplate(templateId);\n    const template = PROMPT_TEMPLATES.find(t => t.id === templateId);\n    if (template) {\n      setDetectionPrompt(template.prompt);\n    }\n  };\n\n  // 绘制图像和检测结果\n  useEffect(() => {\n    if (!canvasRef.current || !previewUrl) return;\n\n    const canvas = canvasRef.current;\n    const ctx = canvas.getContext('2d');\n    if (!ctx) return;\n\n    const img = new Image();\n    img.onload = () => {\n      // 设置画布大小\n      canvas.width = img.width;\n      canvas.height = img.height;\n\n      // 清除画布\n      ctx.clearRect(0, 0, canvas.width, canvas.height);\n\n      // 绘制图像\n      ctx.drawImage(img, 0, 0);\n\n      // 绘制检测结果\n      detectionResults.forEach(result => {\n        const { box, class: className, score } = result;\n        const { xmin, ymin, xmax, ymax } = box;\n\n        // 绘制边界框\n        ctx.strokeStyle = 'rgba(255, 0, 0, 0.8)';\n        ctx.lineWidth = 2;\n        ctx.strokeRect(xmin, ymin, xmax - xmin, ymax - ymin);\n\n        // 绘制标签\n        ctx.fillStyle = 'rgba(255, 0, 0, 0.8)';\n        ctx.fillRect(xmin, ymin - 20, (xmax - xmin), 20);\n        ctx.fillStyle = 'white';\n        ctx.font = '12px Arial';\n        ctx.fillText(`${className} (${score.toFixed(2)})`, xmin + 5, ymin - 5);\n      });\n    };\n    img.src = previewUrl;\n  }, [previewUrl, detectionResults]);\n\n  return (\n    <div className=\"App\">\n      <header className=\"App-header\">\n        <h1>多模态图像语义理解系统</h1>\n      </header>\n      <main className=\"App-main\">\n        {/* 左侧控制面板 */}\n        <div className=\"control-panel\">\n          <div className=\"detection-controls\">\n            <div className=\"prompt-input\">\n              <label htmlFor=\"detection-prompt-template\">目标检测提示词模板:</label>\n              <select\n                id=\"detection-prompt-template\"\n                value={selectedTemplate}\n                onChange={(e) => handleTemplateChange(e.target.value)}\n                className=\"template-select\"\n              >\n                {PROMPT_TEMPLATES.map(template => (\n                  <option key={template.id} value={template.id}>\n                    {template.name}\n                  </option>\n                ))}\n              </select>\n            </div>\n            <div className=\"prompt-input\">\n              <label htmlFor=\"detection-prompt\">自定义目标检测提示词:</label>\n              <input\n                type=\"text\"\n                id=\"detection-prompt\"\n                value={detectionPrompt}\n                onChange={(e) => setDetectionPrompt(e.target.value)}\n                placeholder=\"输入要检测的目标类别，如人、车辆、直升机...\"\n              />\n            </div>\n            <button\n              className=\"detect-btn\"\n              onClick={handleDetect}\n              disabled={!previewUrl || isLoading}\n            >\n              {isLoading ? '检测中...' : '开始检测目标'}\n            </button>\n          </div>\n          \n          {error && <div className=\"error-message\">{error}</div>}\n        </div>\n        \n        {/* 右侧内容区域 */}\n        <div className=\"content-area\">\n          <div className=\"upload-container\">\n            <div {...getRootProps()} className=\"drop-area\">\n              <input {...getInputProps() as any} />\n              <div className=\"drop-area-content\">\n                {!previewUrl ? (\n                  <>\n                    <svg\n                      xmlns=\"http://www.w3.org/2000/svg\"\n                      width=\"48\"\n                      height=\"48\"\n                      viewBox=\"0 0 24 24\"\n                      fill=\"none\"\n                      stroke=\"currentColor\"\n                      strokeWidth=\"2\"\n                      strokeLinecap=\"round\"\n                      strokeLinejoin=\"round\"\n                    >\n                      <path d=\"M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4\" />\n                      <polyline points=\"7 10 12 15 17 10\" />\n                      <line x1=\"12\" y1=\"15\" x2=\"12\" y2=\"3\" />\n                    </svg>\n                    <p>拖放图像到此处或点击选择文件</p>\n                  </>\n                ) : (\n                  <TransformWrapper\n                    initialScale={1}\n                    initialPositionX={0}\n                    initialPositionY={0}\n                  >\n                    {({ zoomIn, zoomOut, resetTransform }: {\n                      zoomIn: () => void;\n                      zoomOut: () => void;\n                      resetTransform: () => void;\n                    }) => (\n                      <div className=\"preview-container\">\n                        <div className=\"preview-controls\">\n                          <button onClick={() => zoomIn()} className=\"control-btn\">放大</button>\n                          <button onClick={() => zoomOut()} className=\"control-btn\">缩小</button>\n                          <button onClick={resetTransform} className=\"control-btn\">重置</button>\n                        </div>\n                        <TransformComponent>\n                          <canvas ref={canvasRef} className=\"preview-canvas\" />\n                        </TransformComponent>\n                      </div>\n                    )}\n                  </TransformWrapper>\n                )}\n              </div>\n            </div>\n          </div>\n\n          <div className=\"results-container\">\n            <h2>检测结果</h2>\n            {detectionResults.length > 0 ? (\n              <div className=\"detection-results\">\n                {detectionResults.map((result, index) => (\n                  <div key={index} className=\"result-item\">\n                    <span className=\"result-class\">{result.class}</span>\n                    <span className=\"result-score\">置信度: {result.score.toFixed(3)}</span>\n                    <div className=\"result-box\">\n                      位置: ({result.box.xmin.toFixed(1)}, {result.box.ymin.toFixed(1)}) 到 ({result.box.xmax.toFixed(1)}, {result.box.ymax.toFixed(1)})\n                    </div>\n                  </div>\n                ))}\n              </div>\n            ) : (\n              <p className=\"no-results\">暂无检测结果</p>\n            )}\n          </div>\n\n          <div className=\"analysis-container\">\n            <h2>态势分析</h2>\n            {situationAnalysis ? (\n              <div className=\"analysis-results\">\n                <p className=\"analysis-summary\">{situationAnalysis.summary}</p>\n                {situationAnalysis.class_distribution && (\n                  <div className=\"class-distribution\">\n                    <h3>类别分布</h3>\n                    <ul>\n                      {Object.entries(situationAnalysis.class_distribution).map(([cls, count]) => (\n                        <li key={cls}>{cls}: {count as number}个</li>\n                      ))}\n                    </ul>\n                  </div>\n                )}\n                {situationAnalysis.details && (\n                  <div className=\"analysis-details\">\n                    <h3>详细信息</h3>\n                    <ul>\n                      {situationAnalysis.details.map((detail: string, index: number) => (\n                        <li key={index}>{detail}</li>\n                      ))}\n                    </ul>\n                  </div>\n                )}\n              </div>\n            ) : (\n              <p className=\"no-analysis\">暂无态势分析结果</p>\n            )}\n          </div>\n        </div>\n      </main>\n      <footer className=\"App-footer\">\n        <p>© 2025 多模态图像语义理解系统</p>\n      </footer>\n    </div>\n  );\n}\n\nexport default App;"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,MAAM,EAAEC,SAAS,QAAQ,OAAO;AAC1D,SAASC,WAAW,QAAQ,gBAAgB;AAC5C,OAAOC,KAAK,MAAM,OAAO;AACzB,SAASC,kBAAkB,EAAEC,gBAAgB,QAAQ,sBAAsB;AAC3E,OAAO,WAAW;;AAElB;AAAA,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAOA;AACA,MAAMC,gBAAkC,GAAG,CACzC;EAAEC,EAAE,EAAE,SAAS;EAAEC,IAAI,EAAE,MAAM;EAAEC,MAAM,EAAE;AAAa,CAAC,EACrD;EAAEF,EAAE,EAAE,QAAQ;EAAEC,IAAI,EAAE,MAAM;EAAEC,MAAM,EAAE;AAAW,CAAC,EAClD;EAAEF,EAAE,EAAE,SAAS;EAAEC,IAAI,EAAE,MAAM;EAAEC,MAAM,EAAE;AAAwB,CAAC,EAChE;EAAEF,EAAE,EAAE,YAAY;EAAEC,IAAI,EAAE,OAAO;EAAEC,MAAM,EAAE;AAAY,CAAC,EACxD;EAAEF,EAAE,EAAE,UAAU;EAAEC,IAAI,EAAE,MAAM;EAAEC,MAAM,EAAE;AAAW,CAAC,EACpD;EAAEF,EAAE,EAAE,MAAM;EAAEC,IAAI,EAAE,MAAM;EAAEC,MAAM,EAAE;AAAW,CAAC,EAChD;EAAEF,EAAE,EAAE,QAAQ;EAAEC,IAAI,EAAE,MAAM;EAAEC,MAAM,EAAE;AAAW,CAAC,EAClD;EAAEF,EAAE,EAAE,UAAU;EAAEC,IAAI,EAAE,OAAO;EAAEC,MAAM,EAAE;AAAY,CAAC,CACvD;AAED,SAASC,GAAGA,CAAA,EAAG;EAAAC,EAAA;EACb,MAAM,CAACC,aAAa,EAAEC,gBAAgB,CAAC,GAAGlB,QAAQ,CAAc,IAAI,CAAC;EACrE,MAAM,CAACmB,UAAU,EAAEC,aAAa,CAAC,GAAGpB,QAAQ,CAAgB,IAAI,CAAC;EACjE,MAAM,CAACqB,eAAe,EAAEC,kBAAkB,CAAC,GAAGtB,QAAQ,CAAS,EAAE,CAAC;EAClE,MAAM,CAACuB,gBAAgB,EAAEC,mBAAmB,CAAC,GAAGxB,QAAQ,CAAS,SAAS,CAAC;EAC3E,MAAM,CAACyB,gBAAgB,EAAEC,mBAAmB,CAAC,GAAG1B,QAAQ,CAAQ,EAAE,CAAC;EACnE,MAAM,CAAC2B,iBAAiB,EAAEC,oBAAoB,CAAC,GAAG5B,QAAQ,CAAM,IAAI,CAAC;EACrE,MAAM,CAAC6B,SAAS,EAAEC,YAAY,CAAC,GAAG9B,QAAQ,CAAU,KAAK,CAAC;EAC1D,MAAM,CAAC+B,KAAK,EAAEC,QAAQ,CAAC,GAAGhC,QAAQ,CAAgB,IAAI,CAAC;EACvD,MAAMiC,SAAS,GAAGhC,MAAM,CAAoB,IAAI,CAAC;;EAEjD;EACA,MAAM;IAAEiC,YAAY;IAAEC;EAAc,CAAC,GAAGhC,WAAW,CAAC;IAClDiC,QAAQ,EAAE,KAAK;IAAE;IACjBC,MAAM,EAAE;MAAE,SAAS,EAAE,CAAC,OAAO,EAAE,MAAM,EAAE,MAAM,EAAE,OAAO;IAAE,CAAC;IACzDC,MAAM,EAAGC,aAAa,IAAK;MACzB,IAAIA,aAAa,CAACC,MAAM,GAAG,CAAC,EAAE;QAC5BC,WAAW,CAACF,aAAa,CAAC,CAAC,CAAC,CAAC;MAC/B;IACF,CAAC;IACDG,WAAW,EAAEA,CAAA,KAAM,CAAC,CAAC;IAAE;IACvBC,UAAU,EAAEA,CAAA,KAAM,CAAC,CAAC;IAAE;IACtBC,WAAW,EAAEA,CAAA,KAAM,CAAC,CAAC,CAAE;EACzB,CAAC,CAAC;;EAEF;EACA,MAAMH,WAAW,GAAII,IAAU,IAAK;IAClC3B,gBAAgB,CAAC2B,IAAI,CAAC;IACtB,MAAMC,GAAG,GAAGC,GAAG,CAACC,eAAe,CAACH,IAAI,CAAC;IACrCzB,aAAa,CAAC0B,GAAG,CAAC;IAClBpB,mBAAmB,CAAC,EAAE,CAAC;IACvBE,oBAAoB,CAAC,IAAI,CAAC;IAC1BI,QAAQ,CAAC,IAAI,CAAC;EAChB,CAAC;;EAED;EACA,MAAMiB,YAAY,GAAG,MAAAA,CAAA,KAAY;IAC/B,IAAI,CAAChC,aAAa,EAAE;MAClBe,QAAQ,CAAC,QAAQ,CAAC;MAClB;IACF;IAEAF,YAAY,CAAC,IAAI,CAAC;IAClBE,QAAQ,CAAC,IAAI,CAAC;IAEd,IAAI;MACF,MAAMkB,QAAQ,GAAG,IAAIC,QAAQ,CAAC,CAAC;MAC/BD,QAAQ,CAACE,MAAM,CAAC,OAAO,EAAEnC,aAAa,CAAC;MACvCiC,QAAQ,CAACE,MAAM,CAAC,QAAQ,EAAE/B,eAAe,CAAC;MAE1C,MAAMgC,QAAQ,GAAG,MAAMjD,KAAK,CAACkD,IAAI,CAAC,kCAAkC,EAAEJ,QAAQ,EAAE;QAC9EK,OAAO,EAAE;UAAE,cAAc,EAAE;QAAsB;MACnD,CAAC,CAAC;MAEF7B,mBAAmB,CAAC2B,QAAQ,CAACG,IAAI,CAACC,UAAU,IAAI,EAAE,CAAC;MACnD7B,oBAAoB,CAACyB,QAAQ,CAACG,IAAI,CAACE,kBAAkB,IAAI,IAAI,CAAC;IAChE,CAAC,CAAC,OAAOC,GAAG,EAAE;MACZ3B,QAAQ,CAAC2B,GAAG,YAAYC,KAAK,GAAGD,GAAG,CAACE,OAAO,GAAG,WAAW,CAAC;IAC5D,CAAC,SAAS;MACR/B,YAAY,CAAC,KAAK,CAAC;IACrB;EACF,CAAC;;EAED;EACA,MAAMgC,oBAAoB,GAAIC,UAAkB,IAAK;IACnDvC,mBAAmB,CAACuC,UAAU,CAAC;IAC/B,MAAMC,QAAQ,GAAGrD,gBAAgB,CAACsD,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACtD,EAAE,KAAKmD,UAAU,CAAC;IAChE,IAAIC,QAAQ,EAAE;MACZ1C,kBAAkB,CAAC0C,QAAQ,CAAClD,MAAM,CAAC;IACrC;EACF,CAAC;;EAED;EACAZ,SAAS,CAAC,MAAM;IACd,IAAI,CAAC+B,SAAS,CAACkC,OAAO,IAAI,CAAChD,UAAU,EAAE;IAEvC,MAAMiD,MAAM,GAAGnC,SAAS,CAACkC,OAAO;IAChC,MAAME,GAAG,GAAGD,MAAM,CAACE,UAAU,CAAC,IAAI,CAAC;IACnC,IAAI,CAACD,GAAG,EAAE;IAEV,MAAME,GAAG,GAAG,IAAIC,KAAK,CAAC,CAAC;IACvBD,GAAG,CAACE,MAAM,GAAG,MAAM;MACjB;MACAL,MAAM,CAACM,KAAK,GAAGH,GAAG,CAACG,KAAK;MACxBN,MAAM,CAACO,MAAM,GAAGJ,GAAG,CAACI,MAAM;;MAE1B;MACAN,GAAG,CAACO,SAAS,CAAC,CAAC,EAAE,CAAC,EAAER,MAAM,CAACM,KAAK,EAAEN,MAAM,CAACO,MAAM,CAAC;;MAEhD;MACAN,GAAG,CAACQ,SAAS,CAACN,GAAG,EAAE,CAAC,EAAE,CAAC,CAAC;;MAExB;MACA9C,gBAAgB,CAACqD,OAAO,CAACC,MAAM,IAAI;QACjC,MAAM;UAAEC,GAAG;UAAEC,KAAK,EAAEC,SAAS;UAAEC;QAAM,CAAC,GAAGJ,MAAM;QAC/C,MAAM;UAAEK,IAAI;UAAEC,IAAI;UAAEC,IAAI;UAAEC;QAAK,CAAC,GAAGP,GAAG;;QAEtC;QACAX,GAAG,CAACmB,WAAW,GAAG,sBAAsB;QACxCnB,GAAG,CAACoB,SAAS,GAAG,CAAC;QACjBpB,GAAG,CAACqB,UAAU,CAACN,IAAI,EAAEC,IAAI,EAAEC,IAAI,GAAGF,IAAI,EAAEG,IAAI,GAAGF,IAAI,CAAC;;QAEpD;QACAhB,GAAG,CAACsB,SAAS,GAAG,sBAAsB;QACtCtB,GAAG,CAACuB,QAAQ,CAACR,IAAI,EAAEC,IAAI,GAAG,EAAE,EAAGC,IAAI,GAAGF,IAAI,EAAG,EAAE,CAAC;QAChDf,GAAG,CAACsB,SAAS,GAAG,OAAO;QACvBtB,GAAG,CAACwB,IAAI,GAAG,YAAY;QACvBxB,GAAG,CAACyB,QAAQ,CAAC,GAAGZ,SAAS,KAAKC,KAAK,CAACY,OAAO,CAAC,CAAC,CAAC,GAAG,EAAEX,IAAI,GAAG,CAAC,EAAEC,IAAI,GAAG,CAAC,CAAC;MACxE,CAAC,CAAC;IACJ,CAAC;IACDd,GAAG,CAACyB,GAAG,GAAG7E,UAAU;EACtB,CAAC,EAAE,CAACA,UAAU,EAAEM,gBAAgB,CAAC,CAAC;EAElC,oBACEjB,OAAA;IAAK0E,SAAS,EAAC,KAAK;IAAAe,QAAA,gBAClBzF,OAAA;MAAQ0E,SAAS,EAAC,YAAY;MAAAe,QAAA,eAC5BzF,OAAA;QAAAyF,QAAA,EAAI;MAAW;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACd,CAAC,eACT7F,OAAA;MAAM0E,SAAS,EAAC,UAAU;MAAAe,QAAA,gBAExBzF,OAAA;QAAK0E,SAAS,EAAC,eAAe;QAAAe,QAAA,gBAC5BzF,OAAA;UAAK0E,SAAS,EAAC,oBAAoB;UAAAe,QAAA,gBACjCzF,OAAA;YAAK0E,SAAS,EAAC,cAAc;YAAAe,QAAA,gBAC3BzF,OAAA;cAAO8F,OAAO,EAAC,2BAA2B;cAAAL,QAAA,EAAC;YAAU;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eAC7D7F,OAAA;cACEI,EAAE,EAAC,2BAA2B;cAC9B2F,KAAK,EAAEhF,gBAAiB;cACxBiF,QAAQ,EAAGC,CAAC,IAAK3C,oBAAoB,CAAC2C,CAAC,CAACC,MAAM,CAACH,KAAK,CAAE;cACtDrB,SAAS,EAAC,iBAAiB;cAAAe,QAAA,EAE1BtF,gBAAgB,CAACgG,GAAG,CAAC3C,QAAQ,iBAC5BxD,OAAA;gBAA0B+F,KAAK,EAAEvC,QAAQ,CAACpD,EAAG;gBAAAqF,QAAA,EAC1CjC,QAAQ,CAACnD;cAAI,GADHmD,QAAQ,CAACpD,EAAE;gBAAAsF,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAEhB,CACT;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACI,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CAAC,eACN7F,OAAA;YAAK0E,SAAS,EAAC,cAAc;YAAAe,QAAA,gBAC3BzF,OAAA;cAAO8F,OAAO,EAAC,kBAAkB;cAAAL,QAAA,EAAC;YAAW;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eACrD7F,OAAA;cACEoG,IAAI,EAAC,MAAM;cACXhG,EAAE,EAAC,kBAAkB;cACrB2F,KAAK,EAAElF,eAAgB;cACvBmF,QAAQ,EAAGC,CAAC,IAAKnF,kBAAkB,CAACmF,CAAC,CAACC,MAAM,CAACH,KAAK,CAAE;cACpDM,WAAW,EAAC;YAAyB;cAAAX,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACtC,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC,eACN7F,OAAA;YACE0E,SAAS,EAAC,YAAY;YACtB4B,OAAO,EAAE7D,YAAa;YACtB8D,QAAQ,EAAE,CAAC5F,UAAU,IAAIU,SAAU;YAAAoE,QAAA,EAElCpE,SAAS,GAAG,QAAQ,GAAG;UAAQ;YAAAqE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC1B,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC,EAELtE,KAAK,iBAAIvB,OAAA;UAAK0E,SAAS,EAAC,eAAe;UAAAe,QAAA,EAAElE;QAAK;UAAAmE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACnD,CAAC,eAGN7F,OAAA;QAAK0E,SAAS,EAAC,cAAc;QAAAe,QAAA,gBAC3BzF,OAAA;UAAK0E,SAAS,EAAC,kBAAkB;UAAAe,QAAA,eAC/BzF,OAAA;YAAA,GAAS0B,YAAY,CAAC,CAAC;YAAEgD,SAAS,EAAC,WAAW;YAAAe,QAAA,gBAC5CzF,OAAA;cAAA,GAAW2B,aAAa,CAAC;YAAC;cAAA+D,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAU,CAAC,eACrC7F,OAAA;cAAK0E,SAAS,EAAC,mBAAmB;cAAAe,QAAA,EAC/B,CAAC9E,UAAU,gBACVX,OAAA,CAAAE,SAAA;gBAAAuF,QAAA,gBACEzF,OAAA;kBACEwG,KAAK,EAAC,4BAA4B;kBAClCtC,KAAK,EAAC,IAAI;kBACVC,MAAM,EAAC,IAAI;kBACXsC,OAAO,EAAC,WAAW;kBACnBC,IAAI,EAAC,MAAM;kBACXC,MAAM,EAAC,cAAc;kBACrBC,WAAW,EAAC,GAAG;kBACfC,aAAa,EAAC,OAAO;kBACrBC,cAAc,EAAC,OAAO;kBAAArB,QAAA,gBAEtBzF,OAAA;oBAAM+G,CAAC,EAAC;kBAA2C;oBAAArB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC,eACtD7F,OAAA;oBAAUgH,MAAM,EAAC;kBAAkB;oBAAAtB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC,eACtC7F,OAAA;oBAAMiH,EAAE,EAAC,IAAI;oBAACC,EAAE,EAAC,IAAI;oBAACC,EAAE,EAAC,IAAI;oBAACC,EAAE,EAAC;kBAAG;oBAAA1B,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACpC,CAAC,eACN7F,OAAA;kBAAAyF,QAAA,EAAG;gBAAc;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAG,CAAC;cAAA,eACrB,CAAC,gBAEH7F,OAAA,CAACF,gBAAgB;gBACfuH,YAAY,EAAE,CAAE;gBAChBC,gBAAgB,EAAE,CAAE;gBACpBC,gBAAgB,EAAE,CAAE;gBAAA9B,QAAA,EAEnBA,CAAC;kBAAE+B,MAAM;kBAAEC,OAAO;kBAAEC;gBAIrB,CAAC,kBACC1H,OAAA;kBAAK0E,SAAS,EAAC,mBAAmB;kBAAAe,QAAA,gBAChCzF,OAAA;oBAAK0E,SAAS,EAAC,kBAAkB;oBAAAe,QAAA,gBAC/BzF,OAAA;sBAAQsG,OAAO,EAAEA,CAAA,KAAMkB,MAAM,CAAC,CAAE;sBAAC9C,SAAS,EAAC,aAAa;sBAAAe,QAAA,EAAC;oBAAE;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAQ,CAAC,eACpE7F,OAAA;sBAAQsG,OAAO,EAAEA,CAAA,KAAMmB,OAAO,CAAC,CAAE;sBAAC/C,SAAS,EAAC,aAAa;sBAAAe,QAAA,EAAC;oBAAE;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAQ,CAAC,eACrE7F,OAAA;sBAAQsG,OAAO,EAAEoB,cAAe;sBAAChD,SAAS,EAAC,aAAa;sBAAAe,QAAA,EAAC;oBAAE;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAQ,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACjE,CAAC,eACN7F,OAAA,CAACH,kBAAkB;oBAAA4F,QAAA,eACjBzF,OAAA;sBAAQ2H,GAAG,EAAElG,SAAU;sBAACiD,SAAS,EAAC;oBAAgB;sBAAAgB,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACnC,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAClB;cACN;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACe;YACnB;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAEN7F,OAAA;UAAK0E,SAAS,EAAC,mBAAmB;UAAAe,QAAA,gBAChCzF,OAAA;YAAAyF,QAAA,EAAI;UAAI;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,EACZ5E,gBAAgB,CAACe,MAAM,GAAG,CAAC,gBAC1BhC,OAAA;YAAK0E,SAAS,EAAC,mBAAmB;YAAAe,QAAA,EAC/BxE,gBAAgB,CAACkF,GAAG,CAAC,CAAC5B,MAAM,EAAEqD,KAAK,kBAClC5H,OAAA;cAAiB0E,SAAS,EAAC,aAAa;cAAAe,QAAA,gBACtCzF,OAAA;gBAAM0E,SAAS,EAAC,cAAc;gBAAAe,QAAA,EAAElB,MAAM,CAACE;cAAK;gBAAAiB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eACpD7F,OAAA;gBAAM0E,SAAS,EAAC,cAAc;gBAAAe,QAAA,GAAC,sBAAK,EAAClB,MAAM,CAACI,KAAK,CAACY,OAAO,CAAC,CAAC,CAAC;cAAA;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eACpE7F,OAAA;gBAAK0E,SAAS,EAAC,YAAY;gBAAAe,QAAA,GAAC,iBACrB,EAAClB,MAAM,CAACC,GAAG,CAACI,IAAI,CAACW,OAAO,CAAC,CAAC,CAAC,EAAC,IAAE,EAAChB,MAAM,CAACC,GAAG,CAACK,IAAI,CAACU,OAAO,CAAC,CAAC,CAAC,EAAC,YAAK,EAAChB,MAAM,CAACC,GAAG,CAACM,IAAI,CAACS,OAAO,CAAC,CAAC,CAAC,EAAC,IAAE,EAAChB,MAAM,CAACC,GAAG,CAACO,IAAI,CAACQ,OAAO,CAAC,CAAC,CAAC,EAAC,GAChI;cAAA;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC;YAAA,GALE+B,KAAK;cAAAlC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAMV,CACN;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC,gBAEN7F,OAAA;YAAG0E,SAAS,EAAC,YAAY;YAAAe,QAAA,EAAC;UAAM;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CACpC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CAAC,eAEN7F,OAAA;UAAK0E,SAAS,EAAC,oBAAoB;UAAAe,QAAA,gBACjCzF,OAAA;YAAAyF,QAAA,EAAI;UAAI;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,EACZ1E,iBAAiB,gBAChBnB,OAAA;YAAK0E,SAAS,EAAC,kBAAkB;YAAAe,QAAA,gBAC/BzF,OAAA;cAAG0E,SAAS,EAAC,kBAAkB;cAAAe,QAAA,EAAEtE,iBAAiB,CAAC0G;YAAO;cAAAnC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,EAC9D1E,iBAAiB,CAAC2G,kBAAkB,iBACnC9H,OAAA;cAAK0E,SAAS,EAAC,oBAAoB;cAAAe,QAAA,gBACjCzF,OAAA;gBAAAyF,QAAA,EAAI;cAAI;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACb7F,OAAA;gBAAAyF,QAAA,EACGsC,MAAM,CAACC,OAAO,CAAC7G,iBAAiB,CAAC2G,kBAAkB,CAAC,CAAC3B,GAAG,CAAC,CAAC,CAAC8B,GAAG,EAAEC,KAAK,CAAC,kBACrElI,OAAA;kBAAAyF,QAAA,GAAewC,GAAG,EAAC,IAAE,EAACC,KAAK,EAAW,QAAC;gBAAA,GAA9BD,GAAG;kBAAAvC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAA+B,CAC5C;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACA,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACF,CACN,EACA1E,iBAAiB,CAACgH,OAAO,iBACxBnI,OAAA;cAAK0E,SAAS,EAAC,kBAAkB;cAAAe,QAAA,gBAC/BzF,OAAA;gBAAAyF,QAAA,EAAI;cAAI;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACb7F,OAAA;gBAAAyF,QAAA,EACGtE,iBAAiB,CAACgH,OAAO,CAAChC,GAAG,CAAC,CAACiC,MAAc,EAAER,KAAa,kBAC3D5H,OAAA;kBAAAyF,QAAA,EAAiB2C;gBAAM,GAAdR,KAAK;kBAAAlC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAc,CAC7B;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACA,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACF,CACN;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC,gBAEN7F,OAAA;YAAG0E,SAAS,EAAC,aAAa;YAAAe,QAAA,EAAC;UAAQ;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CACvC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACF,CAAC,eACP7F,OAAA;MAAQ0E,SAAS,EAAC,YAAY;MAAAe,QAAA,eAC5BzF,OAAA;QAAAyF,QAAA,EAAG;MAAkB;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAG;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACnB,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACN,CAAC;AAEV;AAACrF,EAAA,CA5QQD,GAAG;EAAA,QAY8BZ,WAAW;AAAA;AAAA0I,EAAA,GAZ5C9H,GAAG;AA8QZ,eAAeA,GAAG;AAAC,IAAA8H,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
{"ast": null, "code": "var _jsxFileName = \"D:\\\\DevTools\\\\mllmjiuhangv1\\\\frontend\\\\src\\\\App.tsx\",\n  _s = $RefreshSig$();\nimport React, { useState, useRef, useEffect } from 'react';\nimport { useDropzone } from 'react-dropzone';\nimport axios from 'axios';\nimport { TransformComponent, TransformWrapper } from 'react-zoom-pan-pinch';\nimport './App.css';\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nfunction App() {\n  _s();\n  const [selectedImage, setSelectedImage] = useState(null);\n  const [previewUrl, setPreviewUrl] = useState(null);\n  const [detectionPrompt, setDetectionPrompt] = useState('');\n  const [detectionResults, setDetectionResults] = useState([]);\n  const [situationAnalysis, setSituationAnalysis] = useState(null);\n  const [isLoading, setIsLoading] = useState(false);\n  const [error, setError] = useState(null);\n  const canvasRef = useRef(null);\n\n  // 处理拖放文件\n  const {\n    getRootProps,\n    getInputProps\n  } = useDropzone({\n    multiple: false,\n    // 只允许上传单个文件\n    accept: {\n      'image/*': ['.jpeg', '.jpg', '.png', '.webp']\n    },\n    onDrop: acceptedFiles => {\n      if (acceptedFiles.length > 0) {\n        processFile(acceptedFiles[0]);\n      }\n    },\n    onDragEnter: () => {},\n    // 空函数实现 onDragEnter\n    onDragOver: () => {},\n    // 空函数实现 onDragOver\n    onDragLeave: () => {} // 空函数实现 onDragLeave\n  });\n\n  // 处理文件\n  const processFile = file => {\n    setSelectedImage(file);\n    const url = URL.createObjectURL(file);\n    setPreviewUrl(url);\n    setDetectionResults([]);\n    setSituationAnalysis(null);\n    setError(null);\n  };\n\n  // 处理检测请求\n  const handleDetect = async () => {\n    if (!selectedImage) {\n      setError('请先上传图像');\n      return;\n    }\n    setIsLoading(true);\n    setError(null);\n    try {\n      const formData = new FormData();\n      formData.append('image', selectedImage);\n      formData.append('prompt', detectionPrompt);\n      const response = await axios.post('http://localhost:5000/api/detect', formData, {\n        headers: {\n          'Content-Type': 'multipart/form-data'\n        }\n      });\n      setDetectionResults(response.data.detections || []);\n      setSituationAnalysis(response.data.situation_analysis || null);\n    } catch (err) {\n      setError(err instanceof Error ? err.message : '检测过程中发生错误');\n    } finally {\n      setIsLoading(false);\n    }\n  };\n\n  // 绘制图像和检测结果\n  useEffect(() => {\n    if (!canvasRef.current || !previewUrl) return;\n    const canvas = canvasRef.current;\n    const ctx = canvas.getContext('2d');\n    if (!ctx) return;\n    const img = new Image();\n    img.onload = () => {\n      // 设置画布大小\n      canvas.width = img.width;\n      canvas.height = img.height;\n\n      // 清除画布\n      ctx.clearRect(0, 0, canvas.width, canvas.height);\n\n      // 绘制图像\n      ctx.drawImage(img, 0, 0);\n\n      // 绘制检测结果\n      detectionResults.forEach(result => {\n        const {\n          box,\n          class: className,\n          score\n        } = result;\n        const {\n          xmin,\n          ymin,\n          xmax,\n          ymax\n        } = box;\n\n        // 绘制边界框\n        ctx.strokeStyle = 'rgba(255, 0, 0, 0.8)';\n        ctx.lineWidth = 2;\n        ctx.strokeRect(xmin, ymin, xmax - xmin, ymax - ymin);\n\n        // 绘制标签\n        ctx.fillStyle = 'rgba(255, 0, 0, 0.8)';\n        ctx.fillRect(xmin, ymin - 20, xmax - xmin, 20);\n        ctx.fillStyle = 'white';\n        ctx.font = '12px Arial';\n        ctx.fillText(`${className} (${score.toFixed(2)})`, xmin + 5, ymin - 5);\n      });\n    };\n    img.src = previewUrl;\n  }, [previewUrl, detectionResults]);\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"App\",\n    children: [/*#__PURE__*/_jsxDEV(\"header\", {\n      className: \"App-header\",\n      children: /*#__PURE__*/_jsxDEV(\"h1\", {\n        children: \"\\u591A\\u6A21\\u6001\\u56FE\\u50CF\\u8BED\\u4E49\\u7406\\u89E3\\u7CFB\\u7EDF\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 113,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 112,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"main\", {\n      className: \"App-main\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"control-panel\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"detection-controls\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"prompt-input\",\n            children: [/*#__PURE__*/_jsxDEV(\"label\", {\n              htmlFor: \"detection-prompt\",\n              children: \"\\u68C0\\u6D4B\\u63D0\\u793A:\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 120,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n              type: \"text\",\n              id: \"detection-prompt\",\n              value: detectionPrompt,\n              onChange: e => setDetectionPrompt(e.target.value),\n              placeholder: \"\\u8F93\\u5165\\u8981\\u68C0\\u6D4B\\u7684\\u76EE\\u6807\\u7C7B\\u522B...\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 121,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 119,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            className: \"detect-btn\",\n            onClick: handleDetect,\n            disabled: !previewUrl || isLoading,\n            children: isLoading ? '检测中...' : '开始检测'\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 129,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 118,\n          columnNumber: 11\n        }, this), error && /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"error-message\",\n          children: error\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 138,\n          columnNumber: 21\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 117,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"content-area\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"upload-container\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            ...getRootProps(),\n            className: \"drop-area\",\n            children: [/*#__PURE__*/_jsxDEV(\"input\", {\n              ...getInputProps()\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 145,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"drop-area-content\",\n              children: !previewUrl ? /*#__PURE__*/_jsxDEV(_Fragment, {\n                children: [/*#__PURE__*/_jsxDEV(\"svg\", {\n                  xmlns: \"http://www.w3.org/2000/svg\",\n                  width: \"48\",\n                  height: \"48\",\n                  viewBox: \"0 0 24 24\",\n                  fill: \"none\",\n                  stroke: \"currentColor\",\n                  strokeWidth: \"2\",\n                  strokeLinecap: \"round\",\n                  strokeLinejoin: \"round\",\n                  children: [/*#__PURE__*/_jsxDEV(\"path\", {\n                    d: \"M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 160,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"polyline\", {\n                    points: \"7 10 12 15 17 10\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 161,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"line\", {\n                    x1: \"12\",\n                    y1: \"15\",\n                    x2: \"12\",\n                    y2: \"3\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 162,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 149,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                  children: \"\\u62D6\\u653E\\u56FE\\u50CF\\u5230\\u6B64\\u5904\\u6216\\u70B9\\u51FB\\u9009\\u62E9\\u6587\\u4EF6\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 164,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true) : /*#__PURE__*/_jsxDEV(TransformWrapper, {\n                initialScale: 1,\n                initialPositionX: 0,\n                initialPositionY: 0,\n                children: ({\n                  zoomIn,\n                  zoomOut,\n                  resetTransform\n                }) => /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"preview-container\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"preview-controls\",\n                    children: [/*#__PURE__*/_jsxDEV(\"button\", {\n                      onClick: () => zoomIn(),\n                      className: \"control-btn\",\n                      children: \"\\u653E\\u5927\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 179,\n                      columnNumber: 27\n                    }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                      onClick: () => zoomOut(),\n                      className: \"control-btn\",\n                      children: \"\\u7F29\\u5C0F\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 180,\n                      columnNumber: 27\n                    }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                      onClick: resetTransform,\n                      className: \"control-btn\",\n                      children: \"\\u91CD\\u7F6E\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 181,\n                      columnNumber: 27\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 178,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(TransformComponent, {\n                    children: /*#__PURE__*/_jsxDEV(\"canvas\", {\n                      ref: canvasRef,\n                      className: \"preview-canvas\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 184,\n                      columnNumber: 27\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 183,\n                    columnNumber: 25\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 177,\n                  columnNumber: 23\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 167,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 146,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 144,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 143,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"results-container\",\n          children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n            children: \"\\u68C0\\u6D4B\\u7ED3\\u679C\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 195,\n            columnNumber: 13\n          }, this), detectionResults.length > 0 ? /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"detection-results\",\n            children: detectionResults.map((result, index) => /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"result-item\",\n              children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"result-class\",\n                children: result.class\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 200,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"result-score\",\n                children: [\"\\u7F6E\\u4FE1\\u5EA6: \", result.score.toFixed(3)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 201,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"result-box\",\n                children: [\"\\u4F4D\\u7F6E: (\", result.box.xmin.toFixed(1), \", \", result.box.ymin.toFixed(1), \") \\u5230 (\", result.box.xmax.toFixed(1), \", \", result.box.ymax.toFixed(1), \")\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 202,\n                columnNumber: 21\n              }, this)]\n            }, index, true, {\n              fileName: _jsxFileName,\n              lineNumber: 199,\n              columnNumber: 19\n            }, this))\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 197,\n            columnNumber: 15\n          }, this) : /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"no-results\",\n            children: \"\\u6682\\u65E0\\u68C0\\u6D4B\\u7ED3\\u679C\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 209,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 194,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"analysis-container\",\n          children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n            children: \"\\u6001\\u52BF\\u5206\\u6790\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 214,\n            columnNumber: 13\n          }, this), situationAnalysis ? /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"analysis-results\",\n            children: [/*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"analysis-summary\",\n              children: situationAnalysis.summary\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 217,\n              columnNumber: 17\n            }, this), situationAnalysis.class_distribution && /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"class-distribution\",\n              children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n                children: \"\\u7C7B\\u522B\\u5206\\u5E03\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 220,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"ul\", {\n                children: Object.entries(situationAnalysis.class_distribution).map(([cls, count]) => /*#__PURE__*/_jsxDEV(\"li\", {\n                  children: [cls, \": \", count, \"\\u4E2A\"]\n                }, cls, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 223,\n                  columnNumber: 25\n                }, this))\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 221,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 219,\n              columnNumber: 19\n            }, this), situationAnalysis.details && /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"analysis-details\",\n              children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n                children: \"\\u8BE6\\u7EC6\\u4FE1\\u606F\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 230,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"ul\", {\n                children: situationAnalysis.details.map((detail, index) => /*#__PURE__*/_jsxDEV(\"li\", {\n                  children: detail\n                }, index, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 233,\n                  columnNumber: 25\n                }, this))\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 231,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 229,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 216,\n            columnNumber: 15\n          }, this) : /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"no-analysis\",\n            children: \"\\u6682\\u65E0\\u6001\\u52BF\\u5206\\u6790\\u7ED3\\u679C\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 240,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 213,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 142,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 115,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"footer\", {\n      className: \"App-footer\",\n      children: /*#__PURE__*/_jsxDEV(\"p\", {\n        children: \"\\xA9 2025 \\u591A\\u6A21\\u6001\\u56FE\\u50CF\\u8BED\\u4E49\\u7406\\u89E3\\u7CFB\\u7EDF\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 246,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 245,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 111,\n    columnNumber: 5\n  }, this);\n}\n_s(App, \"iGjphbK8LJmTN4AuV/5kYv/hvok=\", false, function () {\n  return [useDropzone];\n});\n_c = App;\nexport default App;\nvar _c;\n$RefreshReg$(_c, \"App\");", "map": {"version": 3, "names": ["React", "useState", "useRef", "useEffect", "useDropzone", "axios", "TransformComponent", "TransformWrapper", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "App", "_s", "selectedImage", "setSelectedImage", "previewUrl", "setPreviewUrl", "detectionPrompt", "setDetectionPrompt", "detectionResults", "setDetectionResults", "situationAnalysis", "setSituationAnalysis", "isLoading", "setIsLoading", "error", "setError", "canvasRef", "getRootProps", "getInputProps", "multiple", "accept", "onDrop", "acceptedFiles", "length", "processFile", "onDragEnter", "onDragOver", "onDragLeave", "file", "url", "URL", "createObjectURL", "handleDetect", "formData", "FormData", "append", "response", "post", "headers", "data", "detections", "situation_analysis", "err", "Error", "message", "current", "canvas", "ctx", "getContext", "img", "Image", "onload", "width", "height", "clearRect", "drawImage", "for<PERSON>ach", "result", "box", "class", "className", "score", "xmin", "ymin", "xmax", "ymax", "strokeStyle", "lineWidth", "strokeRect", "fillStyle", "fillRect", "font", "fillText", "toFixed", "src", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "htmlFor", "type", "id", "value", "onChange", "e", "target", "placeholder", "onClick", "disabled", "xmlns", "viewBox", "fill", "stroke", "strokeWidth", "strokeLinecap", "strokeLinejoin", "d", "points", "x1", "y1", "x2", "y2", "initialScale", "initialPositionX", "initialPositionY", "zoomIn", "zoomOut", "resetTransform", "ref", "map", "index", "summary", "class_distribution", "Object", "entries", "cls", "count", "details", "detail", "_c", "$RefreshReg$"], "sources": ["D:/DevTools/mllmjiuhangv1/frontend/src/App.tsx"], "sourcesContent": ["import React, { useState, useRef, useEffect } from 'react';\nimport { useDropzone } from 'react-dropzone';\nimport axios from 'axios';\nimport { TransformComponent, TransformWrapper } from 'react-zoom-pan-pinch';\nimport './App.css';\n\nfunction App() {\n  const [selectedImage, setSelectedImage] = useState<File | null>(null);\n  const [previewUrl, setPreviewUrl] = useState<string | null>(null);\n  const [detectionPrompt, setDetectionPrompt] = useState<string>('');\n  const [detectionResults, setDetectionResults] = useState<any[]>([]);\n  const [situationAnalysis, setSituationAnalysis] = useState<any>(null);\n  const [isLoading, setIsLoading] = useState<boolean>(false);\n  const [error, setError] = useState<string | null>(null);\n  const canvasRef = useRef<HTMLCanvasElement>(null);\n\n  // 处理拖放文件\n  const { getRootProps, getInputProps } = useDropzone({\n    multiple: false, // 只允许上传单个文件\n    accept: { 'image/*': ['.jpeg', '.jpg', '.png', '.webp'] },\n    onDrop: (acceptedFiles) => {\n      if (acceptedFiles.length > 0) {\n        processFile(acceptedFiles[0]);\n      }\n    },\n    onDragEnter: () => {}, // 空函数实现 onDragEnter\n    onDragOver: () => {}, // 空函数实现 onDragOver\n    onDragLeave: () => {}, // 空函数实现 onDragLeave\n  });\n\n  // 处理文件\n  const processFile = (file: File) => {\n    setSelectedImage(file);\n    const url = URL.createObjectURL(file);\n    setPreviewUrl(url);\n    setDetectionResults([]);\n    setSituationAnalysis(null);\n    setError(null);\n  };\n\n  // 处理检测请求\n  const handleDetect = async () => {\n    if (!selectedImage) {\n      setError('请先上传图像');\n      return;\n    }\n\n    setIsLoading(true);\n    setError(null);\n\n    try {\n      const formData = new FormData();\n      formData.append('image', selectedImage);\n      formData.append('prompt', detectionPrompt);\n\n      const response = await axios.post('http://localhost:5000/api/detect', formData, {\n        headers: { 'Content-Type': 'multipart/form-data' }\n      });\n\n      setDetectionResults(response.data.detections || []);\n      setSituationAnalysis(response.data.situation_analysis || null);\n    } catch (err) {\n      setError(err instanceof Error ? err.message : '检测过程中发生错误');\n    } finally {\n      setIsLoading(false);\n    }\n  };\n\n  // 绘制图像和检测结果\n  useEffect(() => {\n    if (!canvasRef.current || !previewUrl) return;\n\n    const canvas = canvasRef.current;\n    const ctx = canvas.getContext('2d');\n    if (!ctx) return;\n\n    const img = new Image();\n    img.onload = () => {\n      // 设置画布大小\n      canvas.width = img.width;\n      canvas.height = img.height;\n\n      // 清除画布\n      ctx.clearRect(0, 0, canvas.width, canvas.height);\n\n      // 绘制图像\n      ctx.drawImage(img, 0, 0);\n\n      // 绘制检测结果\n      detectionResults.forEach(result => {\n        const { box, class: className, score } = result;\n        const { xmin, ymin, xmax, ymax } = box;\n\n        // 绘制边界框\n        ctx.strokeStyle = 'rgba(255, 0, 0, 0.8)';\n        ctx.lineWidth = 2;\n        ctx.strokeRect(xmin, ymin, xmax - xmin, ymax - ymin);\n\n        // 绘制标签\n        ctx.fillStyle = 'rgba(255, 0, 0, 0.8)';\n        ctx.fillRect(xmin, ymin - 20, (xmax - xmin), 20);\n        ctx.fillStyle = 'white';\n        ctx.font = '12px Arial';\n        ctx.fillText(`${className} (${score.toFixed(2)})`, xmin + 5, ymin - 5);\n      });\n    };\n    img.src = previewUrl;\n  }, [previewUrl, detectionResults]);\n\n  return (\n    <div className=\"App\">\n      <header className=\"App-header\">\n        <h1>多模态图像语义理解系统</h1>\n      </header>\n      <main className=\"App-main\">\n        {/* 左侧控制面板 */}\n        <div className=\"control-panel\">\n          <div className=\"detection-controls\">\n            <div className=\"prompt-input\">\n              <label htmlFor=\"detection-prompt\">检测提示:</label>\n              <input\n                type=\"text\"\n                id=\"detection-prompt\"\n                value={detectionPrompt}\n                onChange={(e) => setDetectionPrompt(e.target.value)}\n                placeholder=\"输入要检测的目标类别...\"\n              />\n            </div>\n            <button\n              className=\"detect-btn\"\n              onClick={handleDetect}\n              disabled={!previewUrl || isLoading}\n            >\n              {isLoading ? '检测中...' : '开始检测'}\n            </button>\n          </div>\n          \n          {error && <div className=\"error-message\">{error}</div>}\n        </div>\n        \n        {/* 右侧内容区域 */}\n        <div className=\"content-area\">\n          <div className=\"upload-container\">\n            <div {...getRootProps()} className=\"drop-area\">\n              <input {...getInputProps() as any} />\n              <div className=\"drop-area-content\">\n                {!previewUrl ? (\n                  <>\n                    <svg\n                      xmlns=\"http://www.w3.org/2000/svg\"\n                      width=\"48\"\n                      height=\"48\"\n                      viewBox=\"0 0 24 24\"\n                      fill=\"none\"\n                      stroke=\"currentColor\"\n                      strokeWidth=\"2\"\n                      strokeLinecap=\"round\"\n                      strokeLinejoin=\"round\"\n                    >\n                      <path d=\"M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4\" />\n                      <polyline points=\"7 10 12 15 17 10\" />\n                      <line x1=\"12\" y1=\"15\" x2=\"12\" y2=\"3\" />\n                    </svg>\n                    <p>拖放图像到此处或点击选择文件</p>\n                  </>\n                ) : (\n                  <TransformWrapper\n                    initialScale={1}\n                    initialPositionX={0}\n                    initialPositionY={0}\n                  >\n                    {({ zoomIn, zoomOut, resetTransform }: {\n                      zoomIn: () => void;\n                      zoomOut: () => void;\n                      resetTransform: () => void;\n                    }) => (\n                      <div className=\"preview-container\">\n                        <div className=\"preview-controls\">\n                          <button onClick={() => zoomIn()} className=\"control-btn\">放大</button>\n                          <button onClick={() => zoomOut()} className=\"control-btn\">缩小</button>\n                          <button onClick={resetTransform} className=\"control-btn\">重置</button>\n                        </div>\n                        <TransformComponent>\n                          <canvas ref={canvasRef} className=\"preview-canvas\" />\n                        </TransformComponent>\n                      </div>\n                    )}\n                  </TransformWrapper>\n                )}\n              </div>\n            </div>\n          </div>\n\n          <div className=\"results-container\">\n            <h2>检测结果</h2>\n            {detectionResults.length > 0 ? (\n              <div className=\"detection-results\">\n                {detectionResults.map((result, index) => (\n                  <div key={index} className=\"result-item\">\n                    <span className=\"result-class\">{result.class}</span>\n                    <span className=\"result-score\">置信度: {result.score.toFixed(3)}</span>\n                    <div className=\"result-box\">\n                      位置: ({result.box.xmin.toFixed(1)}, {result.box.ymin.toFixed(1)}) 到 ({result.box.xmax.toFixed(1)}, {result.box.ymax.toFixed(1)})\n                    </div>\n                  </div>\n                ))}\n              </div>\n            ) : (\n              <p className=\"no-results\">暂无检测结果</p>\n            )}\n          </div>\n\n          <div className=\"analysis-container\">\n            <h2>态势分析</h2>\n            {situationAnalysis ? (\n              <div className=\"analysis-results\">\n                <p className=\"analysis-summary\">{situationAnalysis.summary}</p>\n                {situationAnalysis.class_distribution && (\n                  <div className=\"class-distribution\">\n                    <h3>类别分布</h3>\n                    <ul>\n                      {Object.entries(situationAnalysis.class_distribution).map(([cls, count]) => (\n                        <li key={cls}>{cls}: {count as number}个</li>\n                      ))}\n                    </ul>\n                  </div>\n                )}\n                {situationAnalysis.details && (\n                  <div className=\"analysis-details\">\n                    <h3>详细信息</h3>\n                    <ul>\n                      {situationAnalysis.details.map((detail: string, index: number) => (\n                        <li key={index}>{detail}</li>\n                      ))}\n                    </ul>\n                  </div>\n                )}\n              </div>\n            ) : (\n              <p className=\"no-analysis\">暂无态势分析结果</p>\n            )}\n          </div>\n        </div>\n      </main>\n      <footer className=\"App-footer\">\n        <p>© 2025 多模态图像语义理解系统</p>\n      </footer>\n    </div>\n  );\n}\n\nexport default App;"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,MAAM,EAAEC,SAAS,QAAQ,OAAO;AAC1D,SAASC,WAAW,QAAQ,gBAAgB;AAC5C,OAAOC,KAAK,MAAM,OAAO;AACzB,SAASC,kBAAkB,EAAEC,gBAAgB,QAAQ,sBAAsB;AAC3E,OAAO,WAAW;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAEnB,SAASC,GAAGA,CAAA,EAAG;EAAAC,EAAA;EACb,MAAM,CAACC,aAAa,EAAEC,gBAAgB,CAAC,GAAGd,QAAQ,CAAc,IAAI,CAAC;EACrE,MAAM,CAACe,UAAU,EAAEC,aAAa,CAAC,GAAGhB,QAAQ,CAAgB,IAAI,CAAC;EACjE,MAAM,CAACiB,eAAe,EAAEC,kBAAkB,CAAC,GAAGlB,QAAQ,CAAS,EAAE,CAAC;EAClE,MAAM,CAACmB,gBAAgB,EAAEC,mBAAmB,CAAC,GAAGpB,QAAQ,CAAQ,EAAE,CAAC;EACnE,MAAM,CAACqB,iBAAiB,EAAEC,oBAAoB,CAAC,GAAGtB,QAAQ,CAAM,IAAI,CAAC;EACrE,MAAM,CAACuB,SAAS,EAAEC,YAAY,CAAC,GAAGxB,QAAQ,CAAU,KAAK,CAAC;EAC1D,MAAM,CAACyB,KAAK,EAAEC,QAAQ,CAAC,GAAG1B,QAAQ,CAAgB,IAAI,CAAC;EACvD,MAAM2B,SAAS,GAAG1B,MAAM,CAAoB,IAAI,CAAC;;EAEjD;EACA,MAAM;IAAE2B,YAAY;IAAEC;EAAc,CAAC,GAAG1B,WAAW,CAAC;IAClD2B,QAAQ,EAAE,KAAK;IAAE;IACjBC,MAAM,EAAE;MAAE,SAAS,EAAE,CAAC,OAAO,EAAE,MAAM,EAAE,MAAM,EAAE,OAAO;IAAE,CAAC;IACzDC,MAAM,EAAGC,aAAa,IAAK;MACzB,IAAIA,aAAa,CAACC,MAAM,GAAG,CAAC,EAAE;QAC5BC,WAAW,CAACF,aAAa,CAAC,CAAC,CAAC,CAAC;MAC/B;IACF,CAAC;IACDG,WAAW,EAAEA,CAAA,KAAM,CAAC,CAAC;IAAE;IACvBC,UAAU,EAAEA,CAAA,KAAM,CAAC,CAAC;IAAE;IACtBC,WAAW,EAAEA,CAAA,KAAM,CAAC,CAAC,CAAE;EACzB,CAAC,CAAC;;EAEF;EACA,MAAMH,WAAW,GAAII,IAAU,IAAK;IAClCzB,gBAAgB,CAACyB,IAAI,CAAC;IACtB,MAAMC,GAAG,GAAGC,GAAG,CAACC,eAAe,CAACH,IAAI,CAAC;IACrCvB,aAAa,CAACwB,GAAG,CAAC;IAClBpB,mBAAmB,CAAC,EAAE,CAAC;IACvBE,oBAAoB,CAAC,IAAI,CAAC;IAC1BI,QAAQ,CAAC,IAAI,CAAC;EAChB,CAAC;;EAED;EACA,MAAMiB,YAAY,GAAG,MAAAA,CAAA,KAAY;IAC/B,IAAI,CAAC9B,aAAa,EAAE;MAClBa,QAAQ,CAAC,QAAQ,CAAC;MAClB;IACF;IAEAF,YAAY,CAAC,IAAI,CAAC;IAClBE,QAAQ,CAAC,IAAI,CAAC;IAEd,IAAI;MACF,MAAMkB,QAAQ,GAAG,IAAIC,QAAQ,CAAC,CAAC;MAC/BD,QAAQ,CAACE,MAAM,CAAC,OAAO,EAAEjC,aAAa,CAAC;MACvC+B,QAAQ,CAACE,MAAM,CAAC,QAAQ,EAAE7B,eAAe,CAAC;MAE1C,MAAM8B,QAAQ,GAAG,MAAM3C,KAAK,CAAC4C,IAAI,CAAC,kCAAkC,EAAEJ,QAAQ,EAAE;QAC9EK,OAAO,EAAE;UAAE,cAAc,EAAE;QAAsB;MACnD,CAAC,CAAC;MAEF7B,mBAAmB,CAAC2B,QAAQ,CAACG,IAAI,CAACC,UAAU,IAAI,EAAE,CAAC;MACnD7B,oBAAoB,CAACyB,QAAQ,CAACG,IAAI,CAACE,kBAAkB,IAAI,IAAI,CAAC;IAChE,CAAC,CAAC,OAAOC,GAAG,EAAE;MACZ3B,QAAQ,CAAC2B,GAAG,YAAYC,KAAK,GAAGD,GAAG,CAACE,OAAO,GAAG,WAAW,CAAC;IAC5D,CAAC,SAAS;MACR/B,YAAY,CAAC,KAAK,CAAC;IACrB;EACF,CAAC;;EAED;EACAtB,SAAS,CAAC,MAAM;IACd,IAAI,CAACyB,SAAS,CAAC6B,OAAO,IAAI,CAACzC,UAAU,EAAE;IAEvC,MAAM0C,MAAM,GAAG9B,SAAS,CAAC6B,OAAO;IAChC,MAAME,GAAG,GAAGD,MAAM,CAACE,UAAU,CAAC,IAAI,CAAC;IACnC,IAAI,CAACD,GAAG,EAAE;IAEV,MAAME,GAAG,GAAG,IAAIC,KAAK,CAAC,CAAC;IACvBD,GAAG,CAACE,MAAM,GAAG,MAAM;MACjB;MACAL,MAAM,CAACM,KAAK,GAAGH,GAAG,CAACG,KAAK;MACxBN,MAAM,CAACO,MAAM,GAAGJ,GAAG,CAACI,MAAM;;MAE1B;MACAN,GAAG,CAACO,SAAS,CAAC,CAAC,EAAE,CAAC,EAAER,MAAM,CAACM,KAAK,EAAEN,MAAM,CAACO,MAAM,CAAC;;MAEhD;MACAN,GAAG,CAACQ,SAAS,CAACN,GAAG,EAAE,CAAC,EAAE,CAAC,CAAC;;MAExB;MACAzC,gBAAgB,CAACgD,OAAO,CAACC,MAAM,IAAI;QACjC,MAAM;UAAEC,GAAG;UAAEC,KAAK,EAAEC,SAAS;UAAEC;QAAM,CAAC,GAAGJ,MAAM;QAC/C,MAAM;UAAEK,IAAI;UAAEC,IAAI;UAAEC,IAAI;UAAEC;QAAK,CAAC,GAAGP,GAAG;;QAEtC;QACAX,GAAG,CAACmB,WAAW,GAAG,sBAAsB;QACxCnB,GAAG,CAACoB,SAAS,GAAG,CAAC;QACjBpB,GAAG,CAACqB,UAAU,CAACN,IAAI,EAAEC,IAAI,EAAEC,IAAI,GAAGF,IAAI,EAAEG,IAAI,GAAGF,IAAI,CAAC;;QAEpD;QACAhB,GAAG,CAACsB,SAAS,GAAG,sBAAsB;QACtCtB,GAAG,CAACuB,QAAQ,CAACR,IAAI,EAAEC,IAAI,GAAG,EAAE,EAAGC,IAAI,GAAGF,IAAI,EAAG,EAAE,CAAC;QAChDf,GAAG,CAACsB,SAAS,GAAG,OAAO;QACvBtB,GAAG,CAACwB,IAAI,GAAG,YAAY;QACvBxB,GAAG,CAACyB,QAAQ,CAAC,GAAGZ,SAAS,KAAKC,KAAK,CAACY,OAAO,CAAC,CAAC,CAAC,GAAG,EAAEX,IAAI,GAAG,CAAC,EAAEC,IAAI,GAAG,CAAC,CAAC;MACxE,CAAC,CAAC;IACJ,CAAC;IACDd,GAAG,CAACyB,GAAG,GAAGtE,UAAU;EACtB,CAAC,EAAE,CAACA,UAAU,EAAEI,gBAAgB,CAAC,CAAC;EAElC,oBACEX,OAAA;IAAK+D,SAAS,EAAC,KAAK;IAAAe,QAAA,gBAClB9E,OAAA;MAAQ+D,SAAS,EAAC,YAAY;MAAAe,QAAA,eAC5B9E,OAAA;QAAA8E,QAAA,EAAI;MAAW;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACd,CAAC,eACTlF,OAAA;MAAM+D,SAAS,EAAC,UAAU;MAAAe,QAAA,gBAExB9E,OAAA;QAAK+D,SAAS,EAAC,eAAe;QAAAe,QAAA,gBAC5B9E,OAAA;UAAK+D,SAAS,EAAC,oBAAoB;UAAAe,QAAA,gBACjC9E,OAAA;YAAK+D,SAAS,EAAC,cAAc;YAAAe,QAAA,gBAC3B9E,OAAA;cAAOmF,OAAO,EAAC,kBAAkB;cAAAL,QAAA,EAAC;YAAK;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eAC/ClF,OAAA;cACEoF,IAAI,EAAC,MAAM;cACXC,EAAE,EAAC,kBAAkB;cACrBC,KAAK,EAAE7E,eAAgB;cACvB8E,QAAQ,EAAGC,CAAC,IAAK9E,kBAAkB,CAAC8E,CAAC,CAACC,MAAM,CAACH,KAAK,CAAE;cACpDI,WAAW,EAAC;YAAe;cAAAX,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC5B,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC,eACNlF,OAAA;YACE+D,SAAS,EAAC,YAAY;YACtB4B,OAAO,EAAExD,YAAa;YACtByD,QAAQ,EAAE,CAACrF,UAAU,IAAIQ,SAAU;YAAA+D,QAAA,EAElC/D,SAAS,GAAG,QAAQ,GAAG;UAAM;YAAAgE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACxB,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC,EAELjE,KAAK,iBAAIjB,OAAA;UAAK+D,SAAS,EAAC,eAAe;UAAAe,QAAA,EAAE7D;QAAK;UAAA8D,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACnD,CAAC,eAGNlF,OAAA;QAAK+D,SAAS,EAAC,cAAc;QAAAe,QAAA,gBAC3B9E,OAAA;UAAK+D,SAAS,EAAC,kBAAkB;UAAAe,QAAA,eAC/B9E,OAAA;YAAA,GAASoB,YAAY,CAAC,CAAC;YAAE2C,SAAS,EAAC,WAAW;YAAAe,QAAA,gBAC5C9E,OAAA;cAAA,GAAWqB,aAAa,CAAC;YAAC;cAAA0D,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAU,CAAC,eACrClF,OAAA;cAAK+D,SAAS,EAAC,mBAAmB;cAAAe,QAAA,EAC/B,CAACvE,UAAU,gBACVP,OAAA,CAAAE,SAAA;gBAAA4E,QAAA,gBACE9E,OAAA;kBACE6F,KAAK,EAAC,4BAA4B;kBAClCtC,KAAK,EAAC,IAAI;kBACVC,MAAM,EAAC,IAAI;kBACXsC,OAAO,EAAC,WAAW;kBACnBC,IAAI,EAAC,MAAM;kBACXC,MAAM,EAAC,cAAc;kBACrBC,WAAW,EAAC,GAAG;kBACfC,aAAa,EAAC,OAAO;kBACrBC,cAAc,EAAC,OAAO;kBAAArB,QAAA,gBAEtB9E,OAAA;oBAAMoG,CAAC,EAAC;kBAA2C;oBAAArB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC,eACtDlF,OAAA;oBAAUqG,MAAM,EAAC;kBAAkB;oBAAAtB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC,eACtClF,OAAA;oBAAMsG,EAAE,EAAC,IAAI;oBAACC,EAAE,EAAC,IAAI;oBAACC,EAAE,EAAC,IAAI;oBAACC,EAAE,EAAC;kBAAG;oBAAA1B,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACpC,CAAC,eACNlF,OAAA;kBAAA8E,QAAA,EAAG;gBAAc;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAG,CAAC;cAAA,eACrB,CAAC,gBAEHlF,OAAA,CAACF,gBAAgB;gBACf4G,YAAY,EAAE,CAAE;gBAChBC,gBAAgB,EAAE,CAAE;gBACpBC,gBAAgB,EAAE,CAAE;gBAAA9B,QAAA,EAEnBA,CAAC;kBAAE+B,MAAM;kBAAEC,OAAO;kBAAEC;gBAIrB,CAAC,kBACC/G,OAAA;kBAAK+D,SAAS,EAAC,mBAAmB;kBAAAe,QAAA,gBAChC9E,OAAA;oBAAK+D,SAAS,EAAC,kBAAkB;oBAAAe,QAAA,gBAC/B9E,OAAA;sBAAQ2F,OAAO,EAAEA,CAAA,KAAMkB,MAAM,CAAC,CAAE;sBAAC9C,SAAS,EAAC,aAAa;sBAAAe,QAAA,EAAC;oBAAE;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAQ,CAAC,eACpElF,OAAA;sBAAQ2F,OAAO,EAAEA,CAAA,KAAMmB,OAAO,CAAC,CAAE;sBAAC/C,SAAS,EAAC,aAAa;sBAAAe,QAAA,EAAC;oBAAE;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAQ,CAAC,eACrElF,OAAA;sBAAQ2F,OAAO,EAAEoB,cAAe;sBAAChD,SAAS,EAAC,aAAa;sBAAAe,QAAA,EAAC;oBAAE;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAQ,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACjE,CAAC,eACNlF,OAAA,CAACH,kBAAkB;oBAAAiF,QAAA,eACjB9E,OAAA;sBAAQgH,GAAG,EAAE7F,SAAU;sBAAC4C,SAAS,EAAC;oBAAgB;sBAAAgB,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACnC,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAClB;cACN;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACe;YACnB;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAENlF,OAAA;UAAK+D,SAAS,EAAC,mBAAmB;UAAAe,QAAA,gBAChC9E,OAAA;YAAA8E,QAAA,EAAI;UAAI;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,EACZvE,gBAAgB,CAACe,MAAM,GAAG,CAAC,gBAC1B1B,OAAA;YAAK+D,SAAS,EAAC,mBAAmB;YAAAe,QAAA,EAC/BnE,gBAAgB,CAACsG,GAAG,CAAC,CAACrD,MAAM,EAAEsD,KAAK,kBAClClH,OAAA;cAAiB+D,SAAS,EAAC,aAAa;cAAAe,QAAA,gBACtC9E,OAAA;gBAAM+D,SAAS,EAAC,cAAc;gBAAAe,QAAA,EAAElB,MAAM,CAACE;cAAK;gBAAAiB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eACpDlF,OAAA;gBAAM+D,SAAS,EAAC,cAAc;gBAAAe,QAAA,GAAC,sBAAK,EAAClB,MAAM,CAACI,KAAK,CAACY,OAAO,CAAC,CAAC,CAAC;cAAA;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eACpElF,OAAA;gBAAK+D,SAAS,EAAC,YAAY;gBAAAe,QAAA,GAAC,iBACrB,EAAClB,MAAM,CAACC,GAAG,CAACI,IAAI,CAACW,OAAO,CAAC,CAAC,CAAC,EAAC,IAAE,EAAChB,MAAM,CAACC,GAAG,CAACK,IAAI,CAACU,OAAO,CAAC,CAAC,CAAC,EAAC,YAAK,EAAChB,MAAM,CAACC,GAAG,CAACM,IAAI,CAACS,OAAO,CAAC,CAAC,CAAC,EAAC,IAAE,EAAChB,MAAM,CAACC,GAAG,CAACO,IAAI,CAACQ,OAAO,CAAC,CAAC,CAAC,EAAC,GAChI;cAAA;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC;YAAA,GALEgC,KAAK;cAAAnC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAMV,CACN;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC,gBAENlF,OAAA;YAAG+D,SAAS,EAAC,YAAY;YAAAe,QAAA,EAAC;UAAM;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CACpC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CAAC,eAENlF,OAAA;UAAK+D,SAAS,EAAC,oBAAoB;UAAAe,QAAA,gBACjC9E,OAAA;YAAA8E,QAAA,EAAI;UAAI;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,EACZrE,iBAAiB,gBAChBb,OAAA;YAAK+D,SAAS,EAAC,kBAAkB;YAAAe,QAAA,gBAC/B9E,OAAA;cAAG+D,SAAS,EAAC,kBAAkB;cAAAe,QAAA,EAAEjE,iBAAiB,CAACsG;YAAO;cAAApC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,EAC9DrE,iBAAiB,CAACuG,kBAAkB,iBACnCpH,OAAA;cAAK+D,SAAS,EAAC,oBAAoB;cAAAe,QAAA,gBACjC9E,OAAA;gBAAA8E,QAAA,EAAI;cAAI;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACblF,OAAA;gBAAA8E,QAAA,EACGuC,MAAM,CAACC,OAAO,CAACzG,iBAAiB,CAACuG,kBAAkB,CAAC,CAACH,GAAG,CAAC,CAAC,CAACM,GAAG,EAAEC,KAAK,CAAC,kBACrExH,OAAA;kBAAA8E,QAAA,GAAeyC,GAAG,EAAC,IAAE,EAACC,KAAK,EAAW,QAAC;gBAAA,GAA9BD,GAAG;kBAAAxC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAA+B,CAC5C;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACA,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACF,CACN,EACArE,iBAAiB,CAAC4G,OAAO,iBACxBzH,OAAA;cAAK+D,SAAS,EAAC,kBAAkB;cAAAe,QAAA,gBAC/B9E,OAAA;gBAAA8E,QAAA,EAAI;cAAI;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACblF,OAAA;gBAAA8E,QAAA,EACGjE,iBAAiB,CAAC4G,OAAO,CAACR,GAAG,CAAC,CAACS,MAAc,EAAER,KAAa,kBAC3DlH,OAAA;kBAAA8E,QAAA,EAAiB4C;gBAAM,GAAdR,KAAK;kBAAAnC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAc,CAC7B;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACA,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACF,CACN;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC,gBAENlF,OAAA;YAAG+D,SAAS,EAAC,aAAa;YAAAe,QAAA,EAAC;UAAQ;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CACvC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACF,CAAC,eACPlF,OAAA;MAAQ+D,SAAS,EAAC,YAAY;MAAAe,QAAA,eAC5B9E,OAAA;QAAA8E,QAAA,EAAG;MAAkB;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAG;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACnB,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACN,CAAC;AAEV;AAAC9E,EAAA,CAnPQD,GAAG;EAAA,QAW8BR,WAAW;AAAA;AAAAgI,EAAA,GAX5CxH,GAAG;AAqPZ,eAAeA,GAAG;AAAC,IAAAwH,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
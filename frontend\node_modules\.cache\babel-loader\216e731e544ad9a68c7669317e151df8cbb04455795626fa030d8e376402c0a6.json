{"ast": null, "code": "var _jsxFileName = \"D:\\\\DevTools\\\\mllmjiuhangv1\\\\frontend\\\\src\\\\components\\\\ResizableSplitter.tsx\",\n  _s = $RefreshSig$();\nimport React, { useState, useRef, useEffect } from 'react';\nimport './ResizableSplitter.css';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst ResizableSplitter = ({\n  onResize,\n  initialWidth = 300,\n  minWidth = 200,\n  maxWidth = 600\n}) => {\n  _s();\n  const [width, setWidth] = useState(initialWidth);\n  const [isResizing, setIsResizing] = useState(false);\n  const splitterRef = useRef(null);\n  useEffect(() => {\n    const handleMouseMove = e => {\n      if (!isResizing) return;\n      const newWidth = Math.max(minWidth, Math.min(maxWidth, e.clientX));\n      setWidth(newWidth);\n      onResize(newWidth);\n    };\n    const handleMouseUp = () => {\n      setIsResizing(false);\n    };\n    if (isResizing) {\n      document.addEventListener('mousemove', handleMouseMove);\n      document.addEventListener('mouseup', handleMouseUp);\n    }\n    return () => {\n      document.removeEventListener('mousemove', handleMouseMove);\n      document.removeEventListener('mouseup', handleMouseUp);\n    };\n  }, [isResizing, minWidth, maxWidth, onResize]);\n  const handleMouseDown = e => {\n    e.preventDefault();\n    setIsResizing(true);\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    ref: splitterRef,\n    className: \"resizable-splitter\",\n    onMouseDown: handleMouseDown\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 51,\n    columnNumber: 5\n  }, this);\n};\n_s(ResizableSplitter, \"0I/zxsiE4bbSBk3iyLGQI03DzSI=\");\n_c = ResizableSplitter;\nexport default ResizableSplitter;\nvar _c;\n$RefreshReg$(_c, \"ResizableSplitter\");", "map": {"version": 3, "names": ["React", "useState", "useRef", "useEffect", "jsxDEV", "_jsxDEV", "ResizableSplitter", "onResize", "initialWidth", "min<PERSON><PERSON><PERSON>", "max<PERSON><PERSON><PERSON>", "_s", "width", "<PERSON><PERSON><PERSON><PERSON>", "isResizing", "setIsResizing", "splitterRef", "handleMouseMove", "e", "newWidth", "Math", "max", "min", "clientX", "handleMouseUp", "document", "addEventListener", "removeEventListener", "handleMouseDown", "preventDefault", "ref", "className", "onMouseDown", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "_c", "$RefreshReg$"], "sources": ["D:/DevTools/mllmjiuhangv1/frontend/src/components/ResizableSplitter.tsx"], "sourcesContent": ["import React, { useState, useRef, useEffect } from 'react';\nimport './ResizableSplitter.css';\n\ninterface ResizableSplitterProps {\n  onResize: (newWidth: number) => void;\n  initialWidth?: number;\n  minWidth?: number;\n  maxWidth?: number;\n}\n\nconst ResizableSplitter: React.FC<ResizableSplitterProps> = ({ \n  onResize, \n  initialWidth = 300, \n  minWidth = 200, \n  maxWidth = 600 \n}) => {\n  const [width, setWidth] = useState(initialWidth);\n  const [isResizing, setIsResizing] = useState(false);\n  const splitterRef = useRef<HTMLDivElement>(null);\n\n  useEffect(() => {\n    const handleMouseMove = (e: MouseEvent) => {\n      if (!isResizing) return;\n      \n      const newWidth = Math.max(minWidth, Math.min(maxWidth, e.clientX));\n      setWidth(newWidth);\n      onResize(newWidth);\n    };\n\n    const handleMouseUp = () => {\n      setIsResizing(false);\n    };\n\n    if (isResizing) {\n      document.addEventListener('mousemove', handleMouseMove);\n      document.addEventListener('mouseup', handleMouseUp);\n    }\n\n    return () => {\n      document.removeEventListener('mousemove', handleMouseMove);\n      document.removeEventListener('mouseup', handleMouseUp);\n    };\n  }, [isResizing, minWidth, maxWidth, onResize]);\n\n  const handleMouseDown = (e: React.MouseEvent) => {\n    e.preventDefault();\n    setIsResizing(true);\n  };\n\n  return (\n    <div \n      ref={splitterRef}\n      className=\"resizable-splitter\"\n      onMouseDown={handleMouseDown}\n    />\n  );\n};\n\nexport default ResizableSplitter;"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,MAAM,EAAEC,SAAS,QAAQ,OAAO;AAC1D,OAAO,yBAAyB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AASjC,MAAMC,iBAAmD,GAAGA,CAAC;EAC3DC,QAAQ;EACRC,YAAY,GAAG,GAAG;EAClBC,QAAQ,GAAG,GAAG;EACdC,QAAQ,GAAG;AACb,CAAC,KAAK;EAAAC,EAAA;EACJ,MAAM,CAACC,KAAK,EAAEC,QAAQ,CAAC,GAAGZ,QAAQ,CAACO,YAAY,CAAC;EAChD,MAAM,CAACM,UAAU,EAAEC,aAAa,CAAC,GAAGd,QAAQ,CAAC,KAAK,CAAC;EACnD,MAAMe,WAAW,GAAGd,MAAM,CAAiB,IAAI,CAAC;EAEhDC,SAAS,CAAC,MAAM;IACd,MAAMc,eAAe,GAAIC,CAAa,IAAK;MACzC,IAAI,CAACJ,UAAU,EAAE;MAEjB,MAAMK,QAAQ,GAAGC,IAAI,CAACC,GAAG,CAACZ,QAAQ,EAAEW,IAAI,CAACE,GAAG,CAACZ,QAAQ,EAAEQ,CAAC,CAACK,OAAO,CAAC,CAAC;MAClEV,QAAQ,CAACM,QAAQ,CAAC;MAClBZ,QAAQ,CAACY,QAAQ,CAAC;IACpB,CAAC;IAED,MAAMK,aAAa,GAAGA,CAAA,KAAM;MAC1BT,aAAa,CAAC,KAAK,CAAC;IACtB,CAAC;IAED,IAAID,UAAU,EAAE;MACdW,QAAQ,CAACC,gBAAgB,CAAC,WAAW,EAAET,eAAe,CAAC;MACvDQ,QAAQ,CAACC,gBAAgB,CAAC,SAAS,EAAEF,aAAa,CAAC;IACrD;IAEA,OAAO,MAAM;MACXC,QAAQ,CAACE,mBAAmB,CAAC,WAAW,EAAEV,eAAe,CAAC;MAC1DQ,QAAQ,CAACE,mBAAmB,CAAC,SAAS,EAAEH,aAAa,CAAC;IACxD,CAAC;EACH,CAAC,EAAE,CAACV,UAAU,EAAEL,QAAQ,EAAEC,QAAQ,EAAEH,QAAQ,CAAC,CAAC;EAE9C,MAAMqB,eAAe,GAAIV,CAAmB,IAAK;IAC/CA,CAAC,CAACW,cAAc,CAAC,CAAC;IAClBd,aAAa,CAAC,IAAI,CAAC;EACrB,CAAC;EAED,oBACEV,OAAA;IACEyB,GAAG,EAAEd,WAAY;IACjBe,SAAS,EAAC,oBAAoB;IAC9BC,WAAW,EAAEJ;EAAgB;IAAAK,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAC9B,CAAC;AAEN,CAAC;AAACzB,EAAA,CA9CIL,iBAAmD;AAAA+B,EAAA,GAAnD/B,iBAAmD;AAgDzD,eAAeA,iBAAiB;AAAC,IAAA+B,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
import React, { useState, useRef, useEffect } from 'react';
import './ResizableSplitter.css';

interface ResizableSplitterProps {
  onResize: (newWidth: number) => void;
  initialWidth?: number;
  minWidth?: number;
  maxWidth?: number;
}

const ResizableSplitter: React.FC<ResizableSplitterProps> = ({ 
  onResize, 
  initialWidth = 300, 
  minWidth = 200, 
  maxWidth = 600 
}) => {
  const [isResizing, setIsResizing] = useState(false);
  const splitterRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    const handleMouseMove = (e: MouseEvent) => {
      if (!isResizing) return;
      
      const newWidth = Math.max(minWidth, Math.min(maxWidth, e.clientX));
      onResize(newWidth);
    };

    const handleTouchMove = (e: TouchEvent) => {
      if (!isResizing || !e.touches[0]) return;
      
      const newWidth = Math.max(minWidth, Math.min(maxWidth, e.touches[0].clientX));
      onResize(newWidth);
    };

    const handleMouseUp = () => {
      setIsResizing(false);
    };

    const handleTouchEnd = () => {
      setIsResizing(false);
    };

    if (isResizing) {
      document.addEventListener('mousemove', handleMouseMove);
      document.addEventListener('mouseup', handleMouseUp);
      document.addEventListener('touchmove', handleTouchMove);
      document.addEventListener('touchend', handleTouchEnd);
    }

    return () => {
      document.removeEventListener('mousemove', handleMouseMove);
      document.removeEventListener('mouseup', handleMouseUp);
      document.removeEventListener('touchmove', handleTouchMove);
      document.removeEventListener('touchend', handleTouchEnd);
    };
  }, [isResizing, minWidth, maxWidth, onResize]);

  const handleMouseDown = (e: React.MouseEvent) => {
    e.preventDefault();
    setIsResizing(true);
  };

  const handleTouchStart = (e: React.TouchEvent) => {
    e.preventDefault();
    setIsResizing(true);
  };

  return (
    <div 
      ref={splitterRef}
      className="resizable-splitter"
      onMouseDown={handleMouseDown}
      onTouchStart={handleTouchStart}
    />
  );
};

export default ResizableSplitter;
import React, { useState, useRef, useEffect } from 'react';
import { useDropzone } from 'react-dropzone';
import axios from 'axios';
import { TransformComponent, TransformWrapper } from 'react-zoom-pan-pinch';
import ResizableSplitter from './components/ResizableSplitter';
import './App.css';

// 定义提示词模板类型
interface PromptTemplate {
  id: string;
  name: string;
  prompt: string;
}

// 预定义的提示词模板
const PROMPT_TEMPLATES: PromptTemplate[] = [
  { id: 'default', name: '默认检测', prompt: '检测图像中的所有目标' },
  { id: 'person', name: '人员检测', prompt: '检测图像中的人员' },
  { id: 'vehicle', name: '车辆检测', prompt: '检测图像中的车辆，包括汽车、卡车、摩托车等' },
  { id: 'helicopter', name: '直升机检测', prompt: '检测图像中的直升机' },
  { id: 'airplane', name: '飞机检测', prompt: '检测图像中的飞机' },
  { id: 'ship', name: '船舶检测', prompt: '检测图像中的船舶' },
  { id: 'animal', name: '动物检测', prompt: '检测图像中的动物' },
  { id: 'building', name: '建筑物检测', prompt: '检测图像中的建筑物' },
];

function App() {
  const [selectedImage, setSelectedImage] = useState<File | null>(null);
  const [previewUrl, setPreviewUrl] = useState<string | null>(null);
  const [detectionPrompt, setDetectionPrompt] = useState<string>('');
  const [selectedTemplate, setSelectedTemplate] = useState<string>('default');
  const [detectionResults, setDetectionResults] = useState<any[]>([]);
  const [situationAnalysis, setSituationAnalysis] = useState<any>(null);
  const [isLoading, setIsLoading] = useState<boolean>(false);
  const [error, setError] = useState<string | null>(null);
  const [leftPanelWidth, setLeftPanelWidth] = useState<number>(300);
  const [screenWidth, setScreenWidth] = useState<number>(window.innerWidth);
  const canvasRef = useRef<HTMLCanvasElement>(null);

  // 监听屏幕尺寸变化
  useEffect(() => {
    const handleResize = () => {
      setScreenWidth(window.innerWidth);
      // 根据屏幕宽度调整默认面板宽度
      if (window.innerWidth < 768) {
        setLeftPanelWidth(Math.min(200, window.innerWidth * 0.8));
      } else {
        setLeftPanelWidth(Math.min(300, window.innerWidth * 0.3));
      }
    };

    window.addEventListener('resize', handleResize);
    // 初始化屏幕宽度
    handleResize();
    
    return () => window.removeEventListener('resize', handleResize);
  }, []);

  // 根据屏幕宽度调整主内容区域的最大宽度
  useEffect(() => {
    const appMain = document.querySelector('.App-main');
    if (appMain) {
      if (screenWidth > 1200) {
        (appMain as HTMLElement).style.maxWidth = '1200px';
      } else {
        (appMain as HTMLElement).style.maxWidth = '100%';
      }
    }
  }, [screenWidth]);

  // 处理拖放文件
  const { getRootProps, getInputProps } = useDropzone({
    multiple: false, // 只允许上传单个文件
    accept: { 'image/*': ['.jpeg', '.jpg', '.png', '.webp'] },
    onDrop: (acceptedFiles) => {
      if (acceptedFiles.length > 0) {
        processFile(acceptedFiles[0]);
      }
    },
    onDragEnter: () => {}, // 空函数实现 onDragEnter
    onDragOver: () => {}, // 空函数实现 onDragOver
    onDragLeave: () => {}, // 空函数实现 onDragLeave
  });

  // 处理文件
  const processFile = (file: File) => {
    setSelectedImage(file);
    const url = URL.createObjectURL(file);
    setPreviewUrl(url);
    setDetectionResults([]);
    setSituationAnalysis(null);
    setError(null);
  };

  // 处理检测请求
  const handleDetect = async () => {
    if (!selectedImage) {
      setError('请先上传图像');
      return;
    }

    setIsLoading(true);
    setError(null);

    try {
      const formData = new FormData();
      formData.append('image', selectedImage);
      formData.append('prompt', detectionPrompt);

      const response = await axios.post('http://localhost:5000/api/detect', formData, {
        headers: { 'Content-Type': 'multipart/form-data' }
      });

      setDetectionResults(response.data.detections || []);
      setSituationAnalysis(response.data.situation_analysis || null);
    } catch (err) {
      setError(err instanceof Error ? err.message : '检测过程中发生错误');
    } finally {
      setIsLoading(false);
    }
  };

  // 当选择模板时更新提示词
  const handleTemplateChange = (templateId: string) => {
    setSelectedTemplate(templateId);
    const template = PROMPT_TEMPLATES.find(t => t.id === templateId);
    if (template) {
      setDetectionPrompt(template.prompt);
    }
  };

  // 绘制图像和检测结果
  useEffect(() => {
    if (!canvasRef.current || !previewUrl) return;

    const canvas = canvasRef.current;
    const ctx = canvas.getContext('2d');
    if (!ctx) return;

    const img = new Image();
    img.onload = () => {
      // 设置画布大小
      canvas.width = img.width;
      canvas.height = img.height;

      // 清除画布
      ctx.clearRect(0, 0, canvas.width, canvas.height);

      // 绘制图像
      ctx.drawImage(img, 0, 0);

      // 绘制检测结果
      detectionResults.forEach(result => {
        const { box, class: className, score } = result;
        const { xmin, ymin, xmax, ymax } = box;

        // 绘制边界框
        ctx.strokeStyle = 'rgba(255, 0, 0, 0.8)';
        ctx.lineWidth = 2;
        ctx.strokeRect(xmin, ymin, xmax - xmin, ymax - ymin);

        // 绘制标签
        ctx.fillStyle = 'rgba(255, 0, 0, 0.8)';
        ctx.fillRect(xmin, ymin - 20, (xmax - xmin), 20);
        ctx.fillStyle = 'white';
        ctx.font = '12px Arial';
        ctx.fillText(`${className} (${score.toFixed(2)})`, xmin + 5, ymin - 5);
      });
    };
    img.src = previewUrl;
  }, [previewUrl, detectionResults]);

  return (
    <div className="App">
      <header className="App-header">
        <h1>多模态图像语义理解系统</h1>
      </header>
      <main className="App-main">
        {/* 左侧控制面板 */}
        <div className="control-panel" style={{ width: `${leftPanelWidth}px` }}>
          <div className="detection-controls">
            <div className="prompt-input">
              <label htmlFor="detection-prompt-template">目标检测提示词模板:</label>
              <select
                id="detection-prompt-template"
                value={selectedTemplate}
                onChange={(e) => handleTemplateChange(e.target.value)}
                className="template-select"
              >
                {PROMPT_TEMPLATES.map(template => (
                  <option key={template.id} value={template.id}>
                    {template.name}
                  </option>
                ))}
              </select>
            </div>
            <div className="prompt-input">
              <label htmlFor="detection-prompt">自定义目标检测提示词:</label>
              <input
                type="text"
                id="detection-prompt"
                value={detectionPrompt}
                onChange={(e) => setDetectionPrompt(e.target.value)}
                placeholder="输入要检测的目标类别，如人、车辆、直升机..."
              />
            </div>
            <button
              className="detect-btn"
              onClick={handleDetect}
              disabled={!previewUrl || isLoading}
            >
              {isLoading ? '检测中...' : '开始检测目标'}
            </button>
          </div>
          
          {error && <div className="error-message">{error}</div>}
        </div>
        
        {/* 可调整大小的分隔器 */}
        <ResizableSplitter 
          onResize={setLeftPanelWidth}
          initialWidth={leftPanelWidth}
          minWidth={200}
          maxWidth={600}
        />
        
        {/* 响应式分隔器：在小屏幕上隐藏 */}
        <div className="responsive-divider" />
        
        {/* 右侧内容区域 */}
        <div className="content-area">
          <div className="upload-container">
            <div {...getRootProps()} className="drop-area">
              <input {...getInputProps() as any} />
              <div className="drop-area-content">
                {!previewUrl ? (
                  <>
                    <svg
                      xmlns="http://www.w3.org/2000/svg"
                      width="48"
                      height="48"
                      viewBox="0 0 24 24"
                      fill="none"
                      stroke="currentColor"
                      strokeWidth="2"
                      strokeLinecap="round"
                      strokeLinejoin="round"
                    >
                      <path d="M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4" />
                      <polyline points="7 10 12 15 17 10" />
                      <line x1="12" y1="15" x2="12" y2="3" />
                    </svg>
                    <p>拖放图像到此处或点击选择文件</p>
                  </>
                ) : (
                  <TransformWrapper
                    initialScale={1}
                    initialPositionX={0}
                    initialPositionY={0}
                  >
                    {({ zoomIn, zoomOut, resetTransform }: {
                      zoomIn: () => void;
                      zoomOut: () => void;
                      resetTransform: () => void;
                    }) => (
                      <div className="preview-container">
                        <div className="preview-controls">
                          <button onClick={() => zoomIn()} className="control-btn">放大</button>
                          <button onClick={() => zoomOut()} className="control-btn">缩小</button>
                          <button onClick={resetTransform} className="control-btn">重置</button>
                        </div>
                        <TransformComponent>
                          <canvas ref={canvasRef} className="preview-canvas" />
                        </TransformComponent>
                      </div>
                    )}
                  </TransformWrapper>
                )}
              </div>
            </div>
          </div>

          <div className="results-container">
            <h2>检测结果</h2>
            {detectionResults.length > 0 ? (
              <div className="detection-results">
                {detectionResults.map((result, index) => (
                  <div key={index} className="result-item">
                    <span className="result-class">{result.class}</span>
                    <span className="result-score">置信度: {result.score.toFixed(3)}</span>
                    <div className="result-box">
                      位置: ({result.box.xmin.toFixed(1)}, {result.box.ymin.toFixed(1)}) 到 ({result.box.xmax.toFixed(1)}, {result.box.ymax.toFixed(1)})
                    </div>
                  </div>
                ))}
              </div>
            ) : (
              <p className="no-results">暂无检测结果</p>
            )}
          </div>

          <div className="analysis-container">
            <h2>态势分析</h2>
            {situationAnalysis ? (
              <div className="analysis-results">
                <p className="analysis-summary">{situationAnalysis.summary}</p>
                {situationAnalysis.class_distribution && (
                  <div className="class-distribution">
                    <h3>类别分布</h3>
                    <ul>
                      {Object.entries(situationAnalysis.class_distribution).map(([cls, count]) => (
                        <li key={cls}>{cls}: {count as number}个</li>
                      ))}
                    </ul>
                  </div>
                )}
                {situationAnalysis.details && (
                  <div className="analysis-details">
                    <h3>详细信息</h3>
                    <ul>
                      {situationAnalysis.details.map((detail: string, index: number) => (
                        <li key={index}>{detail}</li>
                      ))}
                    </ul>
                  </div>
                )}
              </div>
            ) : (
              <p className="no-analysis">暂无态势分析结果</p>
            )}
          </div>
        </div>
      </main>
      <footer className="App-footer">
        <p>© 2025 多模态图像语义理解系统</p>
      </footer>
    </div>
  );
}

export default App;
